# Production Environment Configuration for EDITINK Backend
# Copy this to .env and update with your actual values

# Database Configuration (OPTIONAL)
# SQLite is used by default. Only set DATABASE_URL if you want to use PostgreSQL
# DATABASE_URL=postgresql://username:<EMAIL>:5432/editink_db

# Flask Configuration
SECRET_KEY=your-super-secure-secret-key-here-generate-a-new-one
FLASK_ENV=production
FLASK_DEBUG=False

# Security Settings
SESSION_COOKIE_SECURE=True
SESSION_COOKIE_HTTPONLY=True
SESSION_COOKIE_SAMESITE=Lax

# CORS Settings (update with your frontend domain)
CORS_ORIGINS=https://your-frontend-domain.com,https://localhost:3000

# OpenAI API Configuration (optional - for AI features)
OPENAI_API_KEY=********************************************************************************************************************************************************************

# AWS Specific Settings (if needed)
# AWS_ACCESS_KEY_ID=your_access_key
# AWS_SECRET_ACCESS_KEY=your_secret_key
# AWS_DEFAULT_REGION=us-east-1

# Logging Configuration
LOG_LEVEL=INFO

# Application Settings
MAX_CONTENT_LENGTH=16777216
UPLOAD_FOLDER=/tmp/uploads
