#!/bin/bash

# 🚀 EDITINK EC2 Setup Script for Amazon Linux
# Run this script on your EC2 instance to set up the environment

set -e  # Exit on any error

echo "🚀 Setting up EDITINK on Amazon Linux EC2..."

# Create systemd service
echo "🔧 Setting up systemd service..."
sudo tee /etc/systemd/system/editink-backend.service > /dev/null <<EOF
[Unit]
Description=EDITINK Backend API
After=network.target

[Service]
Type=exec
User=$USER
Group=$USER
WorkingDirectory=/var/www/editink/TE_BACK
Environment=PATH=/var/www/editink/TE_BACK/venv/bin
ExecStart=/var/www/editink/TE_BACK/venv/bin/gunicorn --bind 0.0.0.0:4999 --workers 2 --timeout 120 app:app
ExecReload=/bin/kill -s HUP \$MAINPID
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF

# Reload systemd and enable service
echo "🔄 Configuring systemd service..."
sudo systemctl daemon-reload
sudo systemctl enable editink-backend

# Test database connection (optional)
echo "🧪 Testing application setup..."
if python3 -c "
import os
from dotenv import load_dotenv
load_dotenv()
print('✅ Environment loaded successfully')
print(f'DATABASE_URL configured: {bool(os.getenv(\"DATABASE_URL\"))}')
"; then
    echo "✅ Application setup completed successfully!"
else
    echo "⚠️  Application setup completed with warnings"
fi

echo ""
echo "🎉 Setup Complete!"
echo ""
echo "📋 Next Steps:"
echo "1. Update DATABASE_URL in .env file with your PostgreSQL credentials"
echo "2. Run database migration: python3 deploy_aws_setup.py"
echo "3. Start the service: sudo systemctl start editink-backend"
echo "4. Check status: sudo systemctl status editink-backend"
echo "5. View logs: sudo journalctl -u editink-backend -f"
echo "6. Test health: curl http://localhost:4999/health"
echo ""
echo "🔧 GitHub Actions Setup:"
echo "1. Add these secrets to your GitHub repository:"
echo "   - EC2_HOST: $(curl -s http://***************/latest/meta-data/public-ipv4 2>/dev/null || echo 'your-ec2-public-ip')"
echo "   - EC2_USER: $USER"
echo "   - EC2_SSH_KEY: (your private key content)"
echo "2. Push your code to trigger automatic deployment"
echo ""
echo "✅ Your EC2 instance is ready for EDITINK deployment!"
