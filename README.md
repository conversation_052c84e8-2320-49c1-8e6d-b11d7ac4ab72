# 🚀 EDITINK Backend

## 📋 Overview

EDITINK Backend is a Flask-based API server that provides:
- Journal abbreviation management with SQLite database
- Article reference processing and storage
- Admin authentication and dashboard
- Autocomplete functionality for article IDs
- RESTful API endpoints for frontend integration

## 🗄️ Database

- **Type**: SQLite (simple, no external database required)
- **File**: `article_references.db`
- **Setup**: Automatic via `setup_sqlite.py`

## 🚀 Quick Start

```bash
# 1. Install dependencies
pip install -r requirements.txt

# 2. Initialize database
python setup_sqlite.py

# 3. Start server
python app.py
```

## 📦 Deployment

See [DEPLOYMENT_GUIDE.md](DEPLOYMENT_GUIDE.md) for complete deployment instructions.