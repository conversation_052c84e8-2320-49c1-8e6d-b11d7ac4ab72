#!/usr/bin/env python3
"""
Check database status and analyze the current situation
"""
import sqlite3
import os
from datetime import datetime

def check_database_status():
    """
    Check the status of both databases and analyze the situation
    """
    print("🔍 DATABASE STATUS CHECK")
    print("=" * 60)
    
    # Database paths
    root_db = 'article_references.db'
    instance_db = 'instance/article_references.db'
    
    print("\n📊 FILE INFORMATION:")
    print("-" * 30)
    
    for db_path, name in [(root_db, 'ROOT DATABASE'), (instance_db, 'INSTANCE DATABASE')]:
        if os.path.exists(db_path):
            size = os.path.getsize(db_path)
            modified = datetime.fromtimestamp(os.path.getmtime(db_path))
            print(f"\n{name}:")
            print(f"  Path: {db_path}")
            print(f"  Size: {size:,} bytes ({size/1024:.1f} KB)")
            print(f"  Last Modified: {modified}")
            
            # Quick content check
            if size > 0:
                try:
                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()
                    
                    # Get table count
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                    tables = cursor.fetchall()
                    table_count = len(tables)
                    
                    print(f"  Tables: {table_count}")
                    
                    # Check key tables for data
                    key_tables = ['users', 'article_files', 'article_references', 'journal_abbreviations']
                    data_summary = {}
                    
                    for table in key_tables:
                        try:
                            cursor.execute(f"SELECT COUNT(*) FROM {table};")
                            count = cursor.fetchone()[0]
                            data_summary[table] = count
                        except:
                            data_summary[table] = "N/A"
                    
                    print(f"  Data Summary:")
                    for table, count in data_summary.items():
                        print(f"    - {table}: {count}")
                    
                    conn.close()
                    
                except Exception as e:
                    print(f"  Error reading database: {e}")
            else:
                print(f"  Status: EMPTY")
        else:
            print(f"\n{name}: NOT FOUND")
    
    print(f"\n🔍 ANALYSIS:")
    print("-" * 30)
    
    # Analyze the situation
    root_exists = os.path.exists(root_db)
    instance_exists = os.path.exists(instance_db)
    
    if root_exists:
        root_size = os.path.getsize(root_db)
        root_modified = datetime.fromtimestamp(os.path.getmtime(root_db))
        
        if root_size > 50_000_000:  # > 50MB
            print("✅ ROOT DATABASE: Large database detected (likely from server)")
            print(f"   - Size: {root_size/1024/1024:.1f} MB")
            print(f"   - Modified: {root_modified}")
            print("   - This appears to be the server database you pulled")
        elif root_size > 1_000_000:  # > 1MB
            print("⚠️  ROOT DATABASE: Medium-sized database")
            print(f"   - Size: {root_size/1024:.1f} KB")
            print("   - Could be your local database or server database")
        else:
            print("📝 ROOT DATABASE: Small database (likely local/fresh)")
            print(f"   - Size: {root_size/1024:.1f} KB")
    
    if instance_exists:
        instance_size = os.path.getsize(instance_db)
        instance_modified = datetime.fromtimestamp(os.path.getmtime(instance_db))
        
        if instance_size == 0:
            print("📋 INSTANCE DATABASE: Empty file")
            print(f"   - Modified: {instance_modified}")
            print("   - This is likely unused/leftover")
        else:
            print("📋 INSTANCE DATABASE: Contains data")
            print(f"   - Size: {instance_size/1024:.1f} KB")
    
    print(f"\n💡 RECOMMENDATIONS:")
    print("-" * 30)
    
    # Check if we can find backup or recovery options
    print("1. Check for backup files or git history")
    print("2. Analyze the current root database content")
    print("3. Determine if this is your local data or server data")
    
    # Look for potential backup files
    backup_patterns = [
        'article_references.db.bak',
        'article_references.db.backup',
        '*.db.bak',
        'backup_*.db'
    ]
    
    print(f"\n🔍 LOOKING FOR BACKUP FILES:")
    print("-" * 30)
    
    import glob
    found_backups = []
    
    for pattern in backup_patterns:
        matches = glob.glob(pattern)
        found_backups.extend(matches)
    
    if found_backups:
        print("✅ Found potential backup files:")
        for backup in found_backups:
            if os.path.exists(backup):
                size = os.path.getsize(backup)
                modified = datetime.fromtimestamp(os.path.getmtime(backup))
                print(f"   - {backup} ({size/1024:.1f} KB, {modified})")
    else:
        print("❌ No backup files found in current directory")
    
    return True

if __name__ == "__main__":
    check_database_status()
