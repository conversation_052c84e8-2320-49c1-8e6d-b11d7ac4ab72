#!/usr/bin/env python3
"""
Simple database analysis script using sqlite3 directly
Analyzes the EDITINK database without Flask dependencies
"""
import sqlite3
import json
import os
from datetime import datetime

def analyze_database_direct():
    """
    Analyze database using direct SQLite connection
    """
    db_path = 'article_references.db'
    
    if not os.path.exists(db_path):
        print(f"❌ Database file not found: {db_path}")
        return False
    
    print("🔍 EDITINK Database Analysis (Direct SQLite)")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # ===== DATABASE INFO =====
        print(f"\n📊 DATABASE INFO")
        print("-" * 30)
        
        file_size = os.path.getsize(db_path)
        print(f"Database file size: {file_size:,} bytes ({file_size/1024:.1f} KB)")
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        table_names = [table[0] for table in tables]
        
        print(f"Total tables: {len(table_names)}")
        print(f"Tables: {', '.join(table_names)}")
        
        # ===== TABLE STRUCTURE =====
        print(f"\n🗂️  TABLE STRUCTURES")
        print("-" * 30)
        
        for table_name in table_names:
            cursor.execute(f"PRAGMA table_info({table_name});")
            columns = cursor.fetchall()
            
            print(f"\n📋 {table_name.upper()} ({len(columns)} columns)")
            for col in columns:
                col_id, name, data_type, not_null, default_val, primary_key = col
                nullable = "NOT NULL" if not_null else "NULL"
                pk_marker = " [PK]" if primary_key else ""
                default_info = f" DEFAULT {default_val}" if default_val else ""
                print(f"   • {name:<25} {data_type:<15} {nullable}{pk_marker}{default_info}")
        
        # ===== DATA COUNTS =====
        print(f"\n📈 DATA SUMMARY")
        print("-" * 30)
        
        for table_name in table_names:
            cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
            count = cursor.fetchone()[0]
            print(f"{table_name:<25}: {count:>6} records")
        
        # ===== DETAILED ANALYSIS FOR MAIN TABLES =====
        
        # Users analysis
        if 'users' in table_names:
            print(f"\n👥 USERS ANALYSIS")
            print("-" * 30)
            
            cursor.execute("SELECT COUNT(*) FROM users;")
            total_users = cursor.fetchone()[0]
            
            if total_users > 0:
                # Role distribution
                cursor.execute("SELECT role, COUNT(*) FROM users GROUP BY role;")
                roles = cursor.fetchall()
                print("Role distribution:")
                for role, count in roles:
                    print(f"   {role:<12}: {count:>3}")
                
                # Active status
                cursor.execute("SELECT is_active, COUNT(*) FROM users GROUP BY is_active;")
                active_status = cursor.fetchall()
                print("Active status:")
                for is_active, count in active_status:
                    status = "Active" if is_active else "Inactive"
                    print(f"   {status:<12}: {count:>3}")
                
                # Recent users
                cursor.execute("SELECT username, role, created_at FROM users ORDER BY created_at DESC LIMIT 5;")
                recent_users = cursor.fetchall()
                print("Recent users:")
                for username, role, created_at in recent_users:
                    print(f"   • {username} ({role}) - {created_at}")
        
        # Article Files analysis
        if 'article_files' in table_names:
            print(f"\n📄 ARTICLE FILES ANALYSIS")
            print("-" * 30)
            
            cursor.execute("SELECT COUNT(*) FROM article_files;")
            total_articles = cursor.fetchone()[0]
            
            if total_articles > 0:
                # Status distribution
                cursor.execute("SELECT status, COUNT(*) FROM article_files GROUP BY status;")
                statuses = cursor.fetchall()
                print("Status distribution:")
                for status, count in statuses:
                    print(f"   {status:<15}: {count:>3}")
                
                # Priority distribution
                cursor.execute("SELECT priority, COUNT(*) FROM article_files GROUP BY priority;")
                priorities = cursor.fetchall()
                print("Priority distribution:")
                for priority, count in priorities:
                    print(f"   {priority:<8}: {count:>3}")
                
                # Recent articles
                cursor.execute("SELECT article_id, status, created_at FROM article_files ORDER BY created_at DESC LIMIT 5;")
                recent_articles = cursor.fetchall()
                print("Recent articles:")
                for article_id, status, created_at in recent_articles:
                    print(f"   • {article_id} ({status}) - {created_at}")
        
        # Article References analysis
        if 'article_references' in table_names:
            print(f"\n📚 ARTICLE REFERENCES ANALYSIS")
            print("-" * 30)
            
            cursor.execute("SELECT COUNT(*) FROM article_references;")
            total_refs = cursor.fetchone()[0]
            
            if total_refs > 0:
                # Total references processed
                cursor.execute("SELECT SUM(total_references) FROM article_references WHERE total_references IS NOT NULL;")
                total_processed = cursor.fetchone()[0] or 0
                print(f"Total references processed: {total_processed:>6}")
                
                # Quality distribution
                cursor.execute("""
                    SELECT 
                        SUM(high_confidence_count) as high,
                        SUM(medium_confidence_count) as medium,
                        SUM(low_confidence_count) as low,
                        SUM(needs_review_count) as needs_review
                    FROM article_references
                """)
                quality_stats = cursor.fetchone()
                
                if quality_stats:
                    print("Quality distribution:")
                    print(f"   High confidence:    {quality_stats[0] or 0:>6}")
                    print(f"   Medium confidence:  {quality_stats[1] or 0:>6}")
                    print(f"   Low confidence:     {quality_stats[2] or 0:>6}")
                    print(f"   Needs review:       {quality_stats[3] or 0:>6}")
                
                # Processing sources
                cursor.execute("SELECT processing_source, COUNT(*) FROM article_references WHERE processing_source IS NOT NULL GROUP BY processing_source;")
                sources = cursor.fetchall()
                if sources:
                    print("Processing sources:")
                    for source, count in sources:
                        print(f"   {source:<15}: {count:>3}")
                
                # Average references per article
                if total_refs > 0:
                    avg_refs = total_processed / total_refs
                    print(f"Avg refs per article:   {avg_refs:>6.1f}")
        
        # Journal Abbreviations analysis
        if 'journal_abbreviations' in table_names:
            print(f"\n📖 JOURNAL ABBREVIATIONS ANALYSIS")
            print("-" * 30)
            
            cursor.execute("SELECT COUNT(*) FROM journal_abbreviations;")
            total_journals = cursor.fetchone()[0]
            
            if total_journals > 0:
                # Source distribution
                cursor.execute("SELECT source, COUNT(*) FROM journal_abbreviations GROUP BY source;")
                sources = cursor.fetchall()
                print("Source distribution:")
                for source, count in sources:
                    print(f"   {source:<12}: {count:>3}")
                
                # Verification status
                cursor.execute("SELECT is_verified, COUNT(*) FROM journal_abbreviations GROUP BY is_verified;")
                verification = cursor.fetchall()
                print("Verification status:")
                for is_verified, count in verification:
                    status = "Verified" if is_verified else "Unverified"
                    print(f"   {status:<12}: {count:>3}")
                
                # Top used journals
                cursor.execute("SELECT full_name, abbreviation, usage_count FROM journal_abbreviations ORDER BY usage_count DESC LIMIT 5;")
                top_journals = cursor.fetchall()
                print("Most used journals:")
                for full_name, abbreviation, usage_count in top_journals:
                    print(f"   • {full_name[:40]:<40} -> {abbreviation} ({usage_count} uses)")
        
        conn.close()
        
        print(f"\n✅ Database analysis completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error analyzing database: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    analyze_database_direct()
