#!/usr/bin/env python3
"""
Create a new user in the database
"""
import os
import sys
from datetime import datetime

# Add the current directory to Python path to import app
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db, User

def create_new_user():
    """
    Create a new user with specified credentials
    """
    print("👤 CREATING NEW USER")
    print("=" * 40)
    
    username = "rakhi"
    password = "YgDjXy6Xy3FR$YoV"
    email = "<EMAIL>"
    role = "Admin"
    
    try:
        with app.app_context():
            # Check if user already exists
            existing_user = User.query.filter_by(username=username).first()
            if existing_user:
                print(f"⚠️  User '{username}' already exists!")
                print("   Updating password instead...")
                existing_user.set_password(password)
                db.session.commit()
                print(f"✅ Password updated for user '{username}'")
                return existing_user
            
            # Create new user
            print(f"📝 Creating new user:")
            print(f"   Username: {username}")
            print(f"   Email: {email}")
            print(f"   Role: {role}")
            print(f"   Password: {'*' * len(password)}")
            
            new_user = User(
                username=username,
                email=email,
                role=role,
                is_active=True,
                created_at=datetime.utcnow()
            )
            
            # Set password (this will hash it automatically)
            new_user.set_password(password)
            
            # Add to database
            db.session.add(new_user)
            db.session.commit()
            
            print(f"✅ User '{username}' created successfully!")
            
            # Verify creation
            user_count = User.query.count()
            print(f"📊 Total users in database: {user_count}")
            
            return new_user
            
    except Exception as e:
        print(f"❌ Error creating user: {e}")
        db.session.rollback()
        return None

def verify_user_login():
    """
    Verify the user can login with the provided credentials
    """
    print(f"\n🔐 VERIFYING LOGIN CREDENTIALS")
    print("=" * 40)
    
    username = "rakhi"
    password = "YgDjXy6Xy3FR$YoV"
    
    try:
        with app.app_context():
            user = User.query.filter_by(username=username).first()
            
            if user:
                print(f"✅ User found: {user.username}")
                print(f"   Email: {user.email}")
                print(f"   Role: {user.role}")
                print(f"   Active: {user.is_active}")
                
                # Test password
                if user.check_password(password):
                    print(f"✅ Password verification: SUCCESS")
                    print(f"🎉 Login credentials are working!")
                    
                    # Update last login
                    user.last_login = datetime.utcnow()
                    db.session.commit()
                    
                    return True
                else:
                    print(f"❌ Password verification: FAILED")
                    return False
            else:
                print(f"❌ User '{username}' not found")
                return False
                
    except Exception as e:
        print(f"❌ Error verifying login: {e}")
        return False

def main():
    """
    Main user creation process
    """
    print("👤 USER CREATION PROCESS")
    print("=" * 50)
    
    # Create user
    user = create_new_user()
    if not user:
        print("💥 User creation failed!")
        return False
    
    # Verify login
    if verify_user_login():
        print(f"\n" + "=" * 50)
        print("🎉 USER CREATION COMPLETED!")
        print()
        print("📋 Login Credentials:")
        print(f"   Username: rakhi")
        print(f"   Password: YgDjXy6Xy3FR$YoV")
        print(f"   Email: <EMAIL>")
        print(f"   Role: Admin")
        print()
        print("🚀 You can now login to the EDITINK system!")
        return True
    else:
        print("⚠️  User created but login verification failed")
        return False

if __name__ == "__main__":
    main()
