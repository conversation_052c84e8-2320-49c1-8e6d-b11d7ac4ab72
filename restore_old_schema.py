#!/usr/bin/env python3
"""
Restore the old comprehensive schema and discard server database
This will recreate the better 7-table schema we analyzed earlier
"""
import os
import sys
from datetime import datetime

# Add the current directory to Python path to import app
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def backup_current_db():
    """
    Backup the current server database before replacing it
    """
    current_db = 'article_references.db'
    backup_name = f'server_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
    
    if os.path.exists(current_db):
        print(f"📦 Creating backup of server database...")
        
        # Copy the file
        import shutil
        shutil.copy2(current_db, backup_name)
        
        size = os.path.getsize(backup_name)
        print(f"✅ Server database backed up as: {backup_name}")
        print(f"   Size: {size/1024/1024:.1f} MB")
        print(f"   Contains: 314 article references, 2414 journals, 1 admin user")
        return backup_name
    else:
        print("❌ No current database found to backup")
        return None

def restore_comprehensive_schema():
    """
    Restore the comprehensive 7-table schema
    """
    print(f"\n🔄 Restoring comprehensive schema...")
    
    try:
        # Import Flask app components
        from app import app, db
        
        with app.app_context():
            print("🗑️  Removing old database...")
            if os.path.exists('article_references.db'):
                os.remove('article_references.db')
            
            print("🏗️  Creating comprehensive database schema...")
            db.create_all()
            
            # Verify tables were created
            inspector = db.inspect(db.engine)
            tables = inspector.get_table_names()
            
            print(f"✅ Created {len(tables)} tables:")
            for table in tables:
                columns = inspector.get_columns(table)
                print(f"   - {table} ({len(columns)} columns)")
            
            return True
            
    except Exception as e:
        print(f"❌ Error restoring schema: {e}")
        import traceback
        traceback.print_exc()
        return False

def add_sample_data():
    """
    Add sample journal data to get started
    """
    print(f"\n📝 Adding sample data...")
    
    try:
        from app import app, db, JournalAbbreviation
        
        with app.app_context():
            # Check if data already exists
            existing_count = JournalAbbreviation.query.count()
            if existing_count > 0:
                print(f"ℹ️  Found {existing_count} existing journal entries, skipping sample data")
                return True
            
            sample_journals = [
                {
                    'full_name': 'Nature',
                    'abbreviation': 'Nature',
                    'source': 'manual',
                    'confidence_score': 1.0,
                    'is_verified': True,
                    'created_by': 1
                },
                {
                    'full_name': 'Science',
                    'abbreviation': 'Science',
                    'source': 'manual',
                    'confidence_score': 1.0,
                    'is_verified': True,
                    'created_by': 1
                },
                {
                    'full_name': 'Cell',
                    'abbreviation': 'Cell',
                    'source': 'manual',
                    'confidence_score': 1.0,
                    'is_verified': True,
                    'created_by': 1
                },
                {
                    'full_name': 'The New England Journal of Medicine',
                    'abbreviation': 'N. Engl. J. Med.',
                    'source': 'manual',
                    'confidence_score': 1.0,
                    'is_verified': True,
                    'created_by': 1
                },
                {
                    'full_name': 'Journal of the American Medical Association',
                    'abbreviation': 'JAMA',
                    'source': 'manual',
                    'confidence_score': 1.0,
                    'is_verified': True,
                    'created_by': 1
                }
            ]
            
            for journal_data in sample_journals:
                journal = JournalAbbreviation(**journal_data)
                db.session.add(journal)
            
            db.session.commit()
            
            print(f"✅ Added {len(sample_journals)} sample journal entries")
            return True
            
    except Exception as e:
        print(f"❌ Error adding sample data: {e}")
        return False

def verify_restoration():
    """
    Verify the restoration was successful
    """
    print(f"\n🔍 Verifying restoration...")
    
    try:
        from app import app, db, User, ArticleFile, ArticleReference, JournalAbbreviation
        
        with app.app_context():
            # Check table counts
            user_count = User.query.count()
            article_file_count = ArticleFile.query.count()
            article_ref_count = ArticleReference.query.count()
            journal_count = JournalAbbreviation.query.count()
            
            print(f"📊 Database Status:")
            print(f"   - Users: {user_count}")
            print(f"   - Article Files: {article_file_count}")
            print(f"   - Article References: {article_ref_count}")
            print(f"   - Journal Abbreviations: {journal_count}")
            
            # Check database size
            if os.path.exists('article_references.db'):
                size = os.path.getsize('article_references.db')
                print(f"   - Database Size: {size/1024:.1f} KB")
            
            print(f"\n✅ Schema restoration completed successfully!")
            print(f"🎯 Ready to start fresh with the comprehensive schema")
            
            return True
            
    except Exception as e:
        print(f"❌ Error verifying restoration: {e}")
        return False

def main():
    """
    Main restoration process
    """
    print("🔄 SCHEMA RESTORATION PROCESS")
    print("=" * 60)
    print("This will:")
    print("1. Backup the current server database (314 articles)")
    print("2. Restore the comprehensive 7-table schema")
    print("3. Add sample journal data")
    print("4. Verify the restoration")
    print()
    
    # Step 1: Backup current database
    backup_file = backup_current_db()
    
    # Step 2: Restore comprehensive schema
    if not restore_comprehensive_schema():
        print("💥 Schema restoration failed!")
        return False
    
    # Step 3: Add sample data
    if not add_sample_data():
        print("⚠️  Sample data creation failed, but schema is restored")
    
    # Step 4: Verify restoration
    if not verify_restoration():
        print("⚠️  Verification failed, but restoration may have succeeded")
    
    print(f"\n" + "=" * 60)
    print("🎉 RESTORATION COMPLETED!")
    print()
    print("📋 Summary:")
    print(f"   - Server database backed up as: {backup_file}")
    print(f"   - Comprehensive schema restored (7 tables)")
    print(f"   - Ready for fresh article processing")
    print()
    print("🚀 Next Steps:")
    print("   1. Create admin users if needed")
    print("   2. Start processing articles")
    print("   3. The old server data is safely backed up")
    
    return True

if __name__ == "__main__":
    main()
