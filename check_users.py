#!/usr/bin/env python3
"""
Check users in the current database
"""
import os
import sys

# Add the current directory to Python path to import app
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db, User

def check_current_users():
    """
    Check users in current database
    """
    print("👥 CHECKING CURRENT DATABASE USERS")
    print("=" * 50)
    
    with app.app_context():
        users = User.query.all()
        print(f"Total users in current database: {len(users)}")
        
        if users:
            print("\n📋 User Details:")
            for i, user in enumerate(users, 1):
                print(f"\n{i}. Username: {user.username}")
                print(f"   Role: {user.role}")
                print(f"   Email: {user.email}")
                print(f"   Active: {user.is_active}")
                print(f"   Created: {user.created_at}")
                print(f"   Last Login: {user.last_login}")
                print(f"   Password Hash: {user.password_hash}")
        else:
            print("\n❌ No users found in the current database")
            print("\nℹ️  The current database has a fresh schema with:")
            print("   - 0 users")
            print("   - 0 articles") 
            print("   - 2,414 journal abbreviations")

def check_backup_users():
    """
    Check users in the backup database
    """
    print("\n\n👥 CHECKING BACKUP DATABASE USERS")
    print("=" * 50)
    
    import sqlite3
    import glob
    
    # Find backup file
    backup_files = glob.glob('server_backup_*.db')
    if not backup_files:
        print("❌ No backup files found")
        return
    
    backup_file = backup_files[0]  # Use first backup
    print(f"📦 Checking backup: {backup_file}")
    
    try:
        conn = sqlite3.connect(backup_file)
        cursor = conn.cursor()
        
        # Check what user tables exist
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%user%';")
        user_tables = [row[0] for row in cursor.fetchall()]
        
        print(f"User tables in backup: {user_tables}")
        
        for table in user_tables:
            print(f"\n📋 Table: {table}")
            
            # Get table structure
            cursor.execute(f"PRAGMA table_info({table});")
            columns = [col[1] for col in cursor.fetchall()]
            print(f"Columns: {columns}")
            
            # Get user data
            cursor.execute(f"SELECT * FROM {table};")
            users = cursor.fetchall()
            
            print(f"Total users: {len(users)}")
            
            if users:
                for i, user in enumerate(users, 1):
                    print(f"\n{i}. User data: {user}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error checking backup: {e}")

if __name__ == "__main__":
    check_current_users()
    check_backup_users()
