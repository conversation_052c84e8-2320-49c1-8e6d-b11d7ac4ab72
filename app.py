import logging
from flask import Flask, request, jsonify, session
from flask_cors import CORS
from werkzeug.utils import secure_filename
from werkzeug.security import generate_password_hash, check_password_hash
import docx
import unicodedata
import os
import time
import re
import requests
import json
from datetime import datetime, timedelta
from dotenv import load_dotenv  # Load environment variables from .env
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy.exc import IntegrityError
from sqlalchemy import or_, func, text
import secrets

load_dotenv()

# Initialize Flask app and CORS
app = Flask(__name__)
CORS(app, supports_credentials=True)

# Configure session management
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', secrets.token_hex(32))
app.config['SESSION_COOKIE_HTTPONLY'] = True
app.config['SESSION_COOKIE_SECURE'] = False  # Set to True in production with HTTPS
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=24)
app.config['MAX_CONTENT_LENGTH'] = 50 * 1024 * 1024  # 50 MB

# --- Database Setup ---
# Use SQLite for simplicity
BASE_DIR = os.path.dirname(os.path.abspath(__file__))

# Check for PostgreSQL configuration first, fallback to SQLite
DATABASE_URL = os.getenv('DATABASE_URL')
if DATABASE_URL:
    app.config['SQLALCHEMY_DATABASE_URI'] = DATABASE_URL
else:
    # Use SQLite for development and production
    DB_PATH = os.path.join(BASE_DIR, 'article_references.db')
    app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{DB_PATH}'

app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize SQLAlchemy
db = SQLAlchemy(app)

# Define comprehensive models inline to avoid import conflicts
class User(db.Model):
    """Stores all user accounts: Coordinator, Technical Editor (TE), Copy Editor (CE), Admin"""
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(100), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    role = db.Column(db.Enum('Admin', 'Coordinator', 'TE', 'CE', name='user_roles'), nullable=False)
    email = db.Column(db.String(255), unique=True)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def to_dict(self):
        return {
            'id': self.id,
            'username': self.username,
            'role': self.role,
            'email': self.email,
            'isActive': self.is_active,
            'createdAt': self.created_at.isoformat() if self.created_at else None,
            'lastLogin': self.last_login.isoformat() if self.last_login else None
        }

class ArticleFile(db.Model):
    """Master record for every article zip processed by the system"""
    __tablename__ = 'article_files'

    id = db.Column(db.Integer, primary_key=True)
    article_id = db.Column(db.String(50), unique=True, nullable=False, index=True)
    journal_code = db.Column(db.String(50), index=True)
    journal_name = db.Column(db.String(255))
    status = db.Column(db.Enum('new', 'processing', 'processed', 'assigned_TE', 'assigned_CE', 'completed', name='article_status'), default='new', index=True)
    current_stage = db.Column(db.String(50))
    assigned_to = db.Column(db.Integer, db.ForeignKey('users.id'), index=True)
    folder_path = db.Column(db.Text)
    priority = db.Column(db.Enum('HIGH', 'MEDIUM', 'LOW', name='priority_levels'), default='MEDIUM')
    deadline = db.Column(db.DateTime)
    original_filename = db.Column(db.String(255))
    file_size = db.Column(db.Integer)
    file_hash = db.Column(db.String(64))  # SHA-256 hash
    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    article_metadata = db.Column(db.JSON)  # Extra data from PE portal JSON

    def to_dict(self):
        return {
            'id': self.id,
            'articleId': self.article_id,
            'journalCode': self.journal_code,
            'journalName': self.journal_name,
            'status': self.status,
            'currentStage': self.current_stage,
            'assignedTo': self.assigned_to,
            'priority': self.priority,
            'deadline': self.deadline.isoformat() if self.deadline else None,
            'originalFilename': self.original_filename,
            'fileSize': self.file_size,
            'createdAt': self.created_at.isoformat() if self.created_at else None,
            'updatedAt': self.updated_at.isoformat() if self.updated_at else None,
            'metadata': self.article_metadata
        }

class ArticleReference(db.Model):
    """Stores raw & processed reference list per article"""
    __tablename__ = 'article_references'

    id = db.Column(db.Integer, primary_key=True)
    article_id = db.Column(db.Integer, db.ForeignKey('article_files.id'), nullable=False, index=True)
    reference_text = db.Column(db.JSON)  # Raw list from manuscript
    processed_references = db.Column(db.JSON)  # Cleaned/standardized references
    processing_metadata = db.Column(db.JSON)  # Session details, validation logs
    total_quality_score = db.Column(db.Float)  # Average quality across refs (0–1)
    source_distribution = db.Column(db.JSON)  # { "pubmed_found": 10, "crossref_found": 8, "not_found": 2 }

    # Analytics summary fields
    total_references = db.Column(db.Integer, default=0)  # Count of individual references
    high_confidence_count = db.Column(db.Integer, default=0)  # quality_score >= 0.8
    medium_confidence_count = db.Column(db.Integer, default=0)  # quality_score 0.5-0.79
    low_confidence_count = db.Column(db.Integer, default=0)  # quality_score < 0.5
    needs_review_count = db.Column(db.Integer, default=0)  # Flagged for review

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    processed_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    processing_source = db.Column(db.String(50))  # e.g., "zip_workflow", "manual"

    def to_dict(self):
        return {
            'id': self.id,
            'articleId': self.article_id,
            'referenceText': self.reference_text,
            'processedReferences': self.processed_references,
            'processingMetadata': self.processing_metadata,
            'totalQualityScore': self.total_quality_score,
            'sourceDistribution': self.source_distribution,
            'totalReferences': self.total_references,
            'highConfidenceCount': self.high_confidence_count,
            'mediumConfidenceCount': self.medium_confidence_count,
            'lowConfidenceCount': self.low_confidence_count,
            'needsReviewCount': self.needs_review_count,
            'createdAt': self.created_at.isoformat() if self.created_at else None,
            'updatedAt': self.updated_at.isoformat() if self.updated_at else None,
            'processedBy': self.processed_by,
            'processingSource': self.processing_source
        }

class JournalAbbreviation(db.Model):
    """Stores all journal abbreviations (manual or generated)"""
    __tablename__ = 'journal_abbreviations'

    id = db.Column(db.Integer, primary_key=True)
    full_name = db.Column(db.String(500), nullable=False, index=True)
    abbreviation = db.Column(db.String(200), nullable=False)
    alternate_names = db.Column(db.JSON)  # Other variations
    source = db.Column(db.String(50), default='manual')
    confidence_score = db.Column(db.Float, default=1.0)
    usage_count = db.Column(db.Integer, default=0)
    is_verified = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))

    __table_args__ = (
        db.UniqueConstraint('full_name', name='uq_journal_full_name'),
        db.Index('idx_journal_full_name_lower', func.lower(full_name)),
    )

    def to_dict(self):
        return {
            'id': self.id,
            'fullName': self.full_name,
            'abbreviation': self.abbreviation,
            'alternateNames': self.alternate_names,
            'source': self.source,
            'confidenceScore': self.confidence_score,
            'usageCount': self.usage_count,
            'isVerified': self.is_verified,
            'createdAt': self.created_at.isoformat() if self.created_at else None,
            'updatedAt': self.updated_at.isoformat() if self.updated_at else None,
            'createdBy': self.created_by
        }

# For backward compatibility, create aliases for the old model names
AdminUser = User  # Keep AdminUser alias for existing code
ArticleReferences = ArticleReference  # Keep ArticleReferences alias

UPLOAD_FOLDER = 'uploads'
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# Configure Logging
logging.basicConfig(
    filename='app.log',
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)


# ===== AUTHENTICATION ENDPOINTS =====

@app.route('/api/auth/login', methods=['POST'])
def user_login():
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')

    if not username or not password:
        return jsonify({'error': 'Username and password required'}), 400

    user = User.query.filter_by(username=username, is_active=True).first()

    if user and user.check_password(password):
        # Store user info in session with role-based access
        session['user_id'] = user.id
        session['username'] = user.username
        session['user_role'] = user.role
        session.permanent = True

        # Backward compatibility for admin endpoints
        if user.role == 'Admin':
            session['admin_id'] = user.id
            session['admin_username'] = user.username

        # Update last login
        user.last_login = datetime.utcnow()
        db.session.commit()

        return jsonify({
            'message': 'Login successful',
            'user': user.to_dict()
        }), 200
    else:
        return jsonify({'error': 'Invalid credentials'}), 401

@app.route('/api/auth/logout', methods=['POST'])
def user_logout():
    session.clear()
    return jsonify({'message': 'Logout successful'}), 200

@app.route('/api/auth/check', methods=['GET'])
def check_auth():
    # Check for new user session first
    if 'user_id' in session:
        user = User.query.get(session['user_id'])
        if user and user.is_active:
            return jsonify({
                'authenticated': True,
                'user': user.to_dict()
            }), 200

    # Backward compatibility check for admin session
    elif 'admin_id' in session:
        user = User.query.get(session['admin_id'])
        if user and user.is_active:
            return jsonify({
                'authenticated': True,
                'user': user.to_dict()
            }), 200

    return jsonify({'authenticated': False}), 200

# Helper function to check user permissions
def check_user_permission(required_roles=None):
    """Check if current user has required role permissions"""
    if 'user_id' not in session and 'admin_id' not in session:
        return False, None

    user_id = session.get('user_id') or session.get('admin_id')
    user = User.query.get(user_id)

    if not user or not user.is_active:
        return False, None

    if required_roles and user.role not in required_roles:
        return False, user

    return True, user

# ===== DASHBOARD ENDPOINTS =====

@app.route('/api/dashboard/stats', methods=['GET'])
def get_dashboard_stats():
    """Get comprehensive analytics from the new database schema"""
    # Check authentication with new user system
    has_permission, user = check_user_permission(['Admin', 'Coordinator'])
    if not has_permission:
        return jsonify({'error': 'Authentication required'}), 401

    try:
        # Get date range from query params (default to last 30 days)
        days = request.args.get('days', 30, type=int)
        start_date = datetime.utcnow() - timedelta(days=days)
        today = datetime.utcnow().date()

        # ===== BASIC ARTICLE STATISTICS =====
        # Get article counts from article_files (master table)
        total_articles = ArticleFile.query.count()
        processed_articles = ArticleReference.query.count()

        # Articles processed today
        articles_today = ArticleReference.query.filter(
            func.date(ArticleReference.created_at) == today
        ).count()

        # Articles in date range
        articles_in_range = ArticleReference.query.filter(
            ArticleReference.created_at >= start_date
        ).count()

        # ===== REFERENCE STATISTICS =====
        # Get total references from the new analytics fields
        reference_stats = db.session.execute(text("""
            SELECT
                COALESCE(SUM(total_references), 0) as total_refs,
                COALESCE(SUM(high_confidence_count), 0) as high_confidence,
                COALESCE(SUM(medium_confidence_count), 0) as medium_confidence,
                COALESCE(SUM(low_confidence_count), 0) as low_confidence,
                COUNT(*) as processed_batches
            FROM article_references
        """)).fetchone()

        total_references = reference_stats[0] or 0
        high_confidence_refs = reference_stats[1] or 0
        medium_confidence_refs = reference_stats[2] or 0
        low_confidence_refs = reference_stats[3] or 0

        # Calculate needs_review using frontend logic: score < 90 OR type === 'NOT_FOUND'
        needs_review_refs = 0

        # Get all articles with their analytics and JSON data
        all_articles = db.session.execute(text("""
            SELECT processed_references, source_distribution, needs_review_count
            FROM article_references
        """)).fetchall()

        for processed_refs, source_dist, needs_review_count in all_articles:
            if processed_refs:
                # Calculate from JSON using frontend logic
                try:
                    refs = json.loads(processed_refs) if isinstance(processed_refs, str) else processed_refs
                    if isinstance(refs, list) and len(refs) > 0:
                        # Valid JSON with references
                        for ref in refs:
                            if isinstance(ref, dict):
                                # Frontend logic: score < 90 OR type === 'NOT_FOUND'
                                ref_score = ref.get('score', 0)
                                ref_type = ref.get('type', '')

                                # Convert score to 0-100 scale if it's in 0-1 scale
                                if ref_score <= 1.0:
                                    ref_score = ref_score * 100

                                if ref_score < 90 or ref_type == 'NOT_FOUND':
                                    needs_review_refs += 1
                    else:
                        # Empty or invalid JSON, fall back to analytics with NOT_FOUND correction
                        article_needs_review = needs_review_count or 0

                        # Get NOT_FOUND count from source distribution
                        if source_dist:
                            try:
                                if isinstance(source_dist, str):
                                    first_parse = json.loads(source_dist)
                                    if isinstance(first_parse, str):
                                        source_data = json.loads(first_parse)
                                    else:
                                        source_data = first_parse
                                else:
                                    source_data = source_dist

                                not_found_count = source_data.get('not_found', source_data.get('NotFound', 0))

                                # Ensure needs_review is at least as many as NOT_FOUND
                                article_needs_review = max(article_needs_review, not_found_count)

                            except Exception:
                                pass

                        needs_review_refs += article_needs_review
                except Exception:
                    # Fall back to analytics fields with NOT_FOUND correction
                    article_needs_review = needs_review_count or 0

                    # Get NOT_FOUND count from source distribution
                    if source_dist:
                        try:
                            if isinstance(source_dist, str):
                                first_parse = json.loads(source_dist)
                                if isinstance(first_parse, str):
                                    source_data = json.loads(first_parse)
                                else:
                                    source_data = first_parse
                            else:
                                source_data = source_dist

                            not_found_count = source_data.get('not_found', source_data.get('NotFound', 0))

                            # Ensure needs_review is at least as many as NOT_FOUND
                            article_needs_review = max(article_needs_review, not_found_count)

                        except Exception:
                            pass

                    needs_review_refs += article_needs_review
            else:
                # No JSON data, use analytics fields but ensure NOT_FOUND are included
                article_needs_review = needs_review_count or 0

                # Get NOT_FOUND count from source distribution
                if source_dist:
                    try:
                        if isinstance(source_dist, str):
                            first_parse = json.loads(source_dist)
                            if isinstance(first_parse, str):
                                source_data = json.loads(first_parse)
                            else:
                                source_data = first_parse
                        else:
                            source_data = source_dist

                        not_found_count = source_data.get('not_found', source_data.get('NotFound', 0))

                        # Ensure needs_review is at least as many as NOT_FOUND
                        article_needs_review = max(article_needs_review, not_found_count)

                    except Exception:
                        pass

                needs_review_refs += article_needs_review
        # ===== SOURCE DISTRIBUTION =====
        # Get source distribution data and parse with Python (SQLite JSON_EXTRACT has issues)
        source_distribution_raw = db.session.execute(text("""
            SELECT source_distribution
            FROM article_references
            WHERE source_distribution IS NOT NULL
        """)).fetchall()

        # Parse JSON and aggregate source counts using Python
        pubmed_total = 0
        crossref_total = 0
        database_total = 0
        manual_total = 0
        not_found_total = 0

        for row in source_distribution_raw:
            try:
                if row[0]:
                    # Handle double-encoded JSON (common with SQLAlchemy JSON fields)
                    raw_data = row[0]
                    if isinstance(raw_data, str):
                        # First parse - might give us a string or dict
                        first_parse = json.loads(raw_data)
                        if isinstance(first_parse, str):
                            # Double-encoded - parse again
                            source_data = json.loads(first_parse)
                        else:
                            # Single-encoded - use as is
                            source_data = first_parse
                    else:
                        # Already a dict
                        source_data = raw_data

                    # Extract values
                    if isinstance(source_data, dict):
                        pubmed_total += source_data.get('PubMed', 0)
                        crossref_total += source_data.get('CrossRef', 0)
                        database_total += source_data.get('Database', 0)
                        manual_total += source_data.get('Manual', 0)
                        not_found_total += source_data.get('NotFound', 0)
            except (json.JSONDecodeError, TypeError, AttributeError):
                continue

        # ===== DAILY STATISTICS =====
        daily_stats = db.session.execute(text("""
            SELECT
                DATE(created_at) as date,
                COUNT(*) as articles,
                COALESCE(SUM(total_references), 0) as ref_count
            FROM article_references
            WHERE created_at >= :start_date
            GROUP BY DATE(created_at)
            ORDER BY date DESC
        """), {'start_date': start_date}).fetchall()

        daily_stats_list = [
            {
                'date': row[0].isoformat() if hasattr(row[0], 'isoformat') else str(row[0]),
                'count': row[1],  # Frontend expects 'count' not 'articles'
                'articles': row[1],  # Keep for backward compatibility
                'references': row[2]
            }
            for row in daily_stats
        ]

        # ===== JOURNAL STATISTICS =====
        total_journals = JournalAbbreviation.query.count()
        verified_journals = JournalAbbreviation.query.filter_by(is_verified=True).count()

        journal_sources = db.session.execute(text("""
            SELECT source, COUNT(*) as count
            FROM journal_abbreviations
            GROUP BY source
            ORDER BY count DESC
        """)).fetchall()

        top_journals = JournalAbbreviation.query.order_by(
            JournalAbbreviation.usage_count.desc()
        ).limit(5).all()

        # ===== USER STATISTICS =====
        total_users = User.query.count()
        active_users = User.query.filter_by(is_active=True).count()

        # ===== PROCESSING EFFICIENCY =====
        # Calculate success rates and quality metrics
        total_processed_refs = high_confidence_refs + medium_confidence_refs + low_confidence_refs
        success_rate = (high_confidence_refs / total_processed_refs * 100) if total_processed_refs > 0 else 0
        review_rate = (needs_review_refs / total_processed_refs * 100) if total_processed_refs > 0 else 0

        # Average references per article
        avg_refs_per_article = (total_references / processed_articles) if processed_articles > 0 else 0

        return jsonify({
            # Basic counts
            'totalArticles': total_articles,
            'processedArticles': processed_articles,
            'articlesToday': articles_today,
            'articlesInRange': articles_in_range,
            'totalReferences': total_references,

            # Quality metrics
            'qualityDistribution': {
                'highConfidence': high_confidence_refs,
                'mediumConfidence': medium_confidence_refs,
                'lowConfidence': low_confidence_refs,
                'needsReview': needs_review_refs,
                'total': total_processed_refs
            },

            # Source distribution
            'sourceDistribution': {
                'pubmed': pubmed_total,
                'crossref': crossref_total,
                'database': database_total,
                'manual': manual_total,
                'notFound': not_found_total
            },

            # Processing metrics
            'processingMetrics': {
                'successRate': round(success_rate, 1),
                'reviewRate': round(review_rate, 1),
                'avgReferencesPerArticle': round(avg_refs_per_article, 1),
                'processingCompletion': round((processed_articles / total_articles * 100) if total_articles > 0 else 0, 1)
            },

            # Daily activity
            'dailyStats': daily_stats_list,

            # Journal data
            'journals': {
                'total': total_journals,
                'verified': verified_journals,
                'verificationRate': round((verified_journals / total_journals * 100) if total_journals > 0 else 0, 1),
                'sources': [{'source': row[0], 'count': row[1]} for row in journal_sources],
                'topUsed': [j.to_dict() for j in top_journals]
            },

            # User data (backward compatibility)
            'admins': {
                'total': total_users,
                'active': active_users
            },

            # Date range info
            'dateRange': {
                'start': start_date.isoformat(),
                'end': datetime.utcnow().isoformat(),
                'days': days
            }
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/dashboard/analytics', methods=['GET'])
def get_detailed_analytics():
    """Get detailed analytics for admin dashboard"""
    if 'admin_id' not in session:
        return jsonify({'error': 'Authentication required'}), 401

    try:
        # Enhanced Processing trends using new metadata (last 30 days)
        processing_trends = db.session.execute(text("""
            SELECT
                DATE(created_at) as date,
                COUNT(*) as articles_processed,
                AVG(COALESCE(total_references, 1)) as avg_references_per_article,
                SUM(CASE WHEN processing_source = 'zip_workflow' THEN 1 ELSE 0 END) as zip_workflow_count,
                SUM(CASE WHEN processing_source = 'manual_entry' THEN 1 ELSE 0 END) as manual_entry_count
            FROM article_references
            WHERE created_at >= DATE('now', '-30 days')
            GROUP BY DATE(created_at)
            ORDER BY date DESC
        """)).fetchall()

        # Enhanced Reference Quality Metrics using new metadata
        reference_quality_metrics = db.session.execute(text("""
            SELECT
                SUM(CASE
                    WHEN processing_metadata IS NOT NULL AND processing_metadata != '' THEN
                        CAST(JSON_EXTRACT(processing_metadata, '$.statistics.pubmedCount') AS INTEGER)
                    ELSE 0
                END) as total_pubmed_found,
                SUM(CASE
                    WHEN processing_metadata IS NOT NULL AND processing_metadata != '' THEN
                        CAST(JSON_EXTRACT(processing_metadata, '$.statistics.crossrefCount') AS INTEGER)
                    ELSE 0
                END) as total_crossref_found,
                SUM(CASE
                    WHEN processing_metadata IS NOT NULL AND processing_metadata != '' THEN
                        CAST(JSON_EXTRACT(processing_metadata, '$.statistics.notFoundCount') AS INTEGER)
                    ELSE 0
                END) as total_not_found,
                AVG(CASE
                    WHEN processing_metadata IS NOT NULL AND processing_metadata != '' THEN
                        CAST(JSON_EXTRACT(processing_metadata, '$.statistics.averageQuality') AS REAL)
                    ELSE 0
                END) as avg_quality_score,
                COUNT(*) as total_articles_with_metadata
            FROM article_references
            WHERE processing_metadata IS NOT NULL AND processing_metadata != ''
        """)).fetchone()

        # Quality Distribution from dedicated analytics fields (more reliable than JSON_EXTRACT)
        quality_distribution = db.session.execute(text("""
            SELECT
                COALESCE(SUM(high_confidence_count), 0) as high_quality,
                COALESCE(SUM(medium_confidence_count), 0) as medium_quality,
                COALESCE(SUM(low_confidence_count), 0) as low_quality
            FROM article_references
            WHERE total_references IS NOT NULL
        """)).fetchone()

        # Journal quality metrics (keeping original for journal data)
        journal_quality_metrics = db.session.execute(text("""
            SELECT
                CASE
                    WHEN confidence_score >= 0.9 THEN 'High'
                    WHEN confidence_score >= 0.7 THEN 'Medium'
                    ELSE 'Low'
                END as quality_tier,
                COUNT(*) as count,
                AVG(usage_count) as avg_usage
            FROM journal_abbreviations
            GROUP BY quality_tier
            ORDER BY
                CASE quality_tier
                    WHEN 'High' THEN 1
                    WHEN 'Medium' THEN 2
                    ELSE 3
                END
        """)).fetchall()

        # System performance metrics
        performance_metrics = {
            'avgProcessingTime': None,  # Could be added with timing data
            'errorRate': 0,  # Could be tracked with error logging
            'systemUptime': None,  # Could be tracked with deployment timestamps
            'databaseSize': {
                'journals': JournalAbbreviation.query.count(),
                'articles': ArticleReferences.query.count(),
                'admins': AdminUser.query.count()
            }
        }

        # Recent activity summary - SQLite compatible
        recent_activity = db.session.execute(text("""
            SELECT
                'article_processed' as activity_type,
                article_id as item_id,
                created_at as timestamp
            FROM article_references
            WHERE created_at >= DATE('now', '-7 days')
            ORDER BY created_at DESC
            LIMIT 10
        """)).fetchall()

        return jsonify({
            'processingTrends': [
                {
                    'date': row[0] if isinstance(row[0], str) else (row[0].isoformat() if row[0] else None),
                    'articlesProcessed': row[1],
                    'avgReferencesPerArticle': float(row[2]) if row[2] else 0,
                    'zipWorkflowCount': row[3] if len(row) > 3 else 0,
                    'manualEntryCount': row[4] if len(row) > 4 else 0
                }
                for row in processing_trends
            ],
            'referenceQualityMetrics': {
                'totalPubmedFound': reference_quality_metrics[0] if reference_quality_metrics else 0,
                'totalCrossrefFound': reference_quality_metrics[1] if reference_quality_metrics else 0,
                'totalNotFound': reference_quality_metrics[2] if reference_quality_metrics else 0,
                'avgQualityScore': float(reference_quality_metrics[3]) if reference_quality_metrics and reference_quality_metrics[3] else 0,
                'totalArticlesWithMetadata': reference_quality_metrics[4] if reference_quality_metrics else 0
            },
            'qualityDistribution': {
                'highQuality': quality_distribution[0] if quality_distribution else 0,
                'mediumQuality': quality_distribution[1] if quality_distribution else 0,
                'lowQuality': quality_distribution[2] if quality_distribution else 0
            },
            'journalQualityMetrics': [
                {
                    'qualityTier': row[0],
                    'count': row[1],
                    'avgUsage': float(row[2]) if row[2] else 0
                }
                for row in journal_quality_metrics
            ],
            'performance': performance_metrics,
            'recentActivity': [
                {
                    'type': row[0],
                    'itemId': row[1],
                    'timestamp': row[2] if isinstance(row[2], str) else (row[2].isoformat() if row[2] else None)
                }
                for row in recent_activity
            ]
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/statistics', methods=['GET'])
def get_simplified_statistics():
    """Get simplified statistics for admin statistics page - only essential metrics"""
    if 'admin_id' not in session:
        return jsonify({'error': 'Authentication required'}), 401

    try:
        # ===== REFERENCES ANALYTICS =====

        # 1. Quality Breakdown - get counts from analytics fields
        quality_stats = db.session.execute(text("""
            SELECT
                COALESCE(SUM(high_confidence_count), 0) as high_confidence,
                COALESCE(SUM(medium_confidence_count), 0) as medium_confidence,
                COALESCE(SUM(low_confidence_count), 0) as low_confidence
            FROM article_references
        """)).fetchone()

        # Calculate needs_review using frontend logic: score < 90 OR type === 'NOT_FOUND'
        needs_review_refs = 0

        # Get all articles with their analytics and JSON data
        all_articles = db.session.execute(text("""
            SELECT processed_references, source_distribution, needs_review_count
            FROM article_references
        """)).fetchall()

        for processed_refs, source_dist, needs_review_count in all_articles:
            if processed_refs:
                # Calculate from JSON using frontend logic
                try:
                    refs = json.loads(processed_refs) if isinstance(processed_refs, str) else processed_refs
                    if isinstance(refs, list) and len(refs) > 0:
                        # Valid JSON with references
                        for ref in refs:
                            if isinstance(ref, dict):
                                # Frontend logic: score < 90 OR type === 'NOT_FOUND'
                                ref_score = ref.get('score', 0)
                                ref_type = ref.get('type', '')

                                # Convert score to 0-100 scale if it's in 0-1 scale
                                if ref_score <= 1.0:
                                    ref_score = ref_score * 100

                                if ref_score < 90 or ref_type == 'NOT_FOUND':
                                    needs_review_refs += 1
                    else:
                        # Empty or invalid JSON, fall back to analytics with NOT_FOUND correction
                        article_needs_review = needs_review_count or 0

                        # Get NOT_FOUND count from source distribution
                        if source_dist:
                            try:
                                if isinstance(source_dist, str):
                                    first_parse = json.loads(source_dist)
                                    if isinstance(first_parse, str):
                                        source_data = json.loads(first_parse)
                                    else:
                                        source_data = first_parse
                                else:
                                    source_data = source_dist

                                not_found_count = source_data.get('not_found', source_data.get('NotFound', 0))

                                # Ensure needs_review is at least as many as NOT_FOUND
                                article_needs_review = max(article_needs_review, not_found_count)

                            except Exception:
                                pass

                        needs_review_refs += article_needs_review
                except Exception:
                    # Fall back to analytics fields with NOT_FOUND correction
                    article_needs_review = needs_review_count or 0

                    # Get NOT_FOUND count from source distribution
                    if source_dist:
                        try:
                            if isinstance(source_dist, str):
                                first_parse = json.loads(source_dist)
                                if isinstance(first_parse, str):
                                    source_data = json.loads(first_parse)
                                else:
                                    source_data = first_parse
                            else:
                                source_data = source_dist

                            not_found_count = source_data.get('not_found', source_data.get('NotFound', 0))

                            # Ensure needs_review is at least as many as NOT_FOUND
                            article_needs_review = max(article_needs_review, not_found_count)

                        except Exception:
                            pass

                    needs_review_refs += article_needs_review
            else:
                # No JSON data, use analytics fields but ensure NOT_FOUND are included
                article_needs_review = needs_review_count or 0

                # Get NOT_FOUND count from source distribution
                if source_dist:
                    try:
                        if isinstance(source_dist, str):
                            first_parse = json.loads(source_dist)
                            if isinstance(first_parse, str):
                                source_data = json.loads(first_parse)
                            else:
                                source_data = first_parse
                        else:
                            source_data = source_dist

                        not_found_count = source_data.get('not_found', source_data.get('NotFound', 0))

                        # Ensure needs_review is at least as many as NOT_FOUND
                        article_needs_review = max(article_needs_review, not_found_count)

                    except Exception:
                        pass

                needs_review_refs += article_needs_review

        # 2. Source Breakdown - parse source_distribution JSON with Python
        source_distribution_raw = db.session.execute(text("""
            SELECT source_distribution
            FROM article_references
            WHERE source_distribution IS NOT NULL
        """)).fetchall()

        # Parse and aggregate source counts
        pubmed_found = 0
        crossref_found = 0
        not_found = 0

        for row in source_distribution_raw:
            try:
                if row[0]:
                    # Handle JSON parsing (same as before)
                    source_dist = row[0]
                    if isinstance(source_dist, str):
                        first_parse = json.loads(source_dist)
                        if isinstance(first_parse, str):
                            source_data = json.loads(first_parse)
                        else:
                            source_data = first_parse
                    else:
                        source_data = source_dist

                    # Extract simplified values
                    if isinstance(source_data, dict):
                        pubmed_found += source_data.get('pubmed_found', source_data.get('PubMed', 0))
                        crossref_found += source_data.get('crossref_found', source_data.get('CrossRef', 0))
                        not_found += source_data.get('not_found', source_data.get('NotFound', 0))

            except (json.JSONDecodeError, TypeError, AttributeError):
                continue

        # ===== JOURNALS ANALYTICS =====

        # 3. Journal Counts - database vs manual
        journal_stats = db.session.execute(text("""
            SELECT
                COUNT(CASE WHEN source != 'manual' THEN 1 END) as database_journals,
                COUNT(CASE WHEN source = 'manual' THEN 1 END) as manual_journals
            FROM journal_abbreviations
        """)).fetchone()

        # ===== RETURN SIMPLIFIED ANALYTICS =====

        return jsonify({
            # REFERENCES ANALYTICS
            'references': {
                # 1. Quality Breakdown
                'quality_breakdown': {
                    'high_confidence': quality_stats[0] if quality_stats else 0,
                    'medium_confidence': quality_stats[1] if quality_stats else 0,
                    'low_confidence': quality_stats[2] if quality_stats else 0,
                    'needs_review': needs_review_refs
                },

                # 2. Review Status (comparison/ratio)
                'review_status': {
                    'total_needs_review': needs_review_refs,
                    'total_high_confidence': quality_stats[0] if quality_stats else 0,
                    'review_ratio': round((needs_review_refs / max(quality_stats[0] if quality_stats else 1, 1)) * 100, 2) if quality_stats and quality_stats[0] > 0 else 0
                },

                # 3. Source Breakdown
                'source_breakdown': {
                    'pubmed_found': pubmed_found,
                    'crossref_found': crossref_found,
                    'not_found': not_found,
                    'total_references': pubmed_found + crossref_found + not_found
                }
            },

            # JOURNALS ANALYTICS
            'journals': {
                'database_journals': journal_stats[0] if journal_stats else 0,
                'manual_journals': journal_stats[1] if journal_stats else 0
            }
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/dashboard/articles', methods=['GET'])
def get_articles_list():
    if 'admin_id' not in session:
        return jsonify({'error': 'Authentication required'}), 401
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        search = request.args.get('search', '', type=str)
        sort_by = request.args.get('sort_by', 'created_at', type=str)
        sort_order = request.args.get('sort_order', 'desc', type=str)
        date_start = request.args.get('date_start', '', type=str)
        date_end = request.args.get('date_end', '', type=str)
        min_refs = request.args.get('min_refs', '', type=str)
        max_refs = request.args.get('max_refs', '', type=str)

        # Join with ArticleFile to get the actual article identifiers and additional metadata
        query = db.session.query(
            ArticleReferences,
            ArticleFile.article_id.label('actual_article_id'),
            ArticleFile.status.label('status'),
            ArticleFile.current_stage.label('current_stage'),
            ArticleFile.priority.label('priority'),
            ArticleFile.journal_name.label('journal_name')
        ).join(
            ArticleFile, ArticleReferences.article_id == ArticleFile.id
        )

        # Apply search filter - search in the actual article ID from ArticleFile
        if search:
            query = query.filter(ArticleFile.article_id.contains(search))
        # Apply date range filter
        if date_start:
            try:
                start_date = datetime.strptime(date_start, '%Y-%m-%d').date()
                query = query.filter(ArticleReferences.created_at >= start_date)
            except ValueError:
                pass

        if date_end:
            try:
                end_date = datetime.strptime(date_end, '%Y-%m-%d').date()
                query = query.filter(ArticleReferences.created_at <= end_date)
            except ValueError:
                pass

        # Apply reference count filters
        if min_refs:
            try:
                min_refs_int = int(min_refs)
                query = query.filter(ArticleReferences.total_references >= min_refs_int)
            except ValueError:
                pass

        if max_refs:
            try:
                max_refs_int = int(max_refs)
                query = query.filter(ArticleReferences.total_references <= max_refs_int)
            except ValueError:
                pass
        # Apply sorting
        if sort_by == 'created_at':
            if sort_order == 'asc':
                query = query.order_by(ArticleReferences.created_at.asc())
            else:
                query = query.order_by(ArticleReferences.created_at.desc())
        elif sort_by == 'total_references':
            if sort_order == 'asc':
                query = query.order_by(ArticleReferences.total_references.asc())
            else:
                query = query.order_by(ArticleReferences.total_references.desc())

        # Paginate results
        paginated = query.paginate(
            page=page, per_page=per_page, error_out=False
        )

        articles = []
        for item in paginated.items:
            # item is a tuple: (ArticleReferences, actual_article_id, status, current_stage, priority, journal_name)
            article = item[0]  # ArticleReferences object
            actual_article_id = item[1]  # The real article ID from ArticleFile
            status = item[2]
            current_stage = item[3]
            priority = item[4]
            journal_name = item[5]

            # Calculate quality metrics
            total_refs = article.total_references or 0
            high_conf = article.high_confidence_count or 0
            needs_review = article.needs_review_count or 0

            success_rate = round((high_conf / total_refs * 100) if total_refs > 0 else 0, 1)
            review_rate = round((needs_review / total_refs * 100) if total_refs > 0 else 0, 1)

            # Parse source distribution
            source_breakdown = {'pubmed': 0, 'crossref': 0, 'not_found': 0}
            if article.source_distribution:
                try:
                    if isinstance(article.source_distribution, str):
                        first_parse = json.loads(article.source_distribution)
                        if isinstance(first_parse, str):
                            source_data = json.loads(first_parse)
                        else:
                            source_data = first_parse
                    else:
                        source_data = article.source_distribution

                    if isinstance(source_data, dict):
                        source_breakdown['pubmed'] = source_data.get('pubmed_found', source_data.get('PubMed', 0))
                        source_breakdown['crossref'] = source_data.get('crossref_found', source_data.get('CrossRef', 0))
                        source_breakdown['not_found'] = source_data.get('not_found', source_data.get('NotFound', 0))
                except Exception:
                    pass

            articles.append({
                'id': article.id,
                'article_id': actual_article_id,  # Use the actual article ID (e.g., 'ija_995_25')
                'articleId': actual_article_id,  # Frontend expects this field name
                'total_references': total_refs,
                'referenceCount': total_refs,  # Frontend expects this field name
                'high_confidence_count': high_conf,
                'medium_confidence_count': article.medium_confidence_count or 0,
                'low_confidence_count': article.low_confidence_count or 0,
                'needs_review_count': needs_review,
                'created_at': article.created_at.isoformat() if article.created_at else None,
                'createdAt': article.created_at.isoformat() if article.created_at else None,  # Frontend expects this field name
                'updated_at': article.updated_at.isoformat() if article.updated_at else None,

                # Enhanced fields
                'status': status,
                'currentStage': current_stage,
                'priority': priority,
                'journalName': journal_name,
                'processedBy': article.processed_by,
                'processingSource': article.processing_source,
                'totalQualityScore': round(article.total_quality_score * 100, 1) if article.total_quality_score else 0,
                'successRate': success_rate,
                'reviewRate': review_rate,
                'sourceBreakdown': source_breakdown
            })

        return jsonify({
            'articles': articles,
            'pagination': {
                'page': paginated.page,
                'pages': paginated.pages,
                'per_page': paginated.per_page,
                'total': paginated.total,
                'has_next': paginated.has_next,
                'has_prev': paginated.has_prev
            }
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500
# File upload endpoint
@app.route('/upload', methods=['POST'])
# File upload endpoint
@app.route('/upload', methods=['POST'])
def upload_file():
    if 'file' not in request.files:
        return jsonify({"error": "No file part in the request"}), 400

    file = request.files['file']

    if file.filename == '':
        return jsonify({"error": "No file selected for uploading"}), 400

    if file and file.filename.endswith('.docx'):
        filename = secure_filename(file.filename)
        timestamp = int(time.time())  # Avoid overwriting
        filename = f"{timestamp}_{filename}"
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)

        try:
            file.save(filepath)
            references = extract_references(filepath)
            return jsonify({"references": references})

        except Exception as e:
            logging.error(f"Error processing file {filename}: {e}")
            return jsonify({"error": "Internal server error"}), 500

    return jsonify({"error": "Invalid file format. Only .docx files are allowed"}), 400
def extract_references(filepath):
    """Extract references from a DOCX file"""
    try:
        doc = Document(filepath)
        references = []

        for paragraph in doc.paragraphs:
            text = paragraph.text.strip()
            if text and (text[0].isdigit() or text.startswith('[')):
                references.append(text)

        return references if references else ["No references found"]
    except Exception as e:
        logging.error(f"Error extracting references: {e}")
        return []

def get_reference_details(reference_text):
    """Get details for a reference using OpenAI API"""
    try:
        client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))

        prompt = f"""
        Analyze this reference and extract the following information:
        Reference: "{reference_text}"

        Please provide:
        1. Journal name (full name)
        2. Journal abbreviation (if different from full name)
        3. Authors
        4. Title
        5. Year
        6. Volume/Issue/Pages

        Format as JSON with keys: journal_full, journal_abbrev, authors, title, year, volume_info
        """

        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=500,
            temperature=0.3
        )

        result = response.choices[0].message.content.strip()

        try:
            return json.loads(result)
        except json.JSONDecodeError:
            return {"error": "Failed to extract details", "response": result}

    except Exception as e:
        return {"error": "Error interacting with OpenAI API", "details": str(e)}

@app.route('/api/save-references', methods=['POST'])
def save_references():
    if 'file' not in request.files:
        return jsonify({"error": "No file part in the request"}), 400

    file = request.files['file']

    if file.filename == '':
        return jsonify({"error": "No file selected for uploading"}), 400

    if file and file.filename.endswith('.docx'):
        filename = secure_filename(file.filename)
        timestamp = int(time.time())  # Avoid overwriting
        filename = f"{timestamp}_{filename}"
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)

        try:
            file.save(filepath)
            references = extract_references(filepath)

            if not references:
                return jsonify({"error": "No references found"}), 400

            return jsonify({"references": references})

        except Exception as e:
            logging.error(f"Error processing file {filename}: {e}")
            return jsonify({"error": "Internal server error"}), 500

    return jsonify({"error": "Only .docx files are allowed"}), 400

def extract_references(filepath):
    try:
        doc = docx.Document(filepath)
        references = []
        is_reference_section = False

        for paragraph in doc.paragraphs:
            text = paragraph.text.strip()

            if not is_reference_section:
            # Detect the start of the References section (handling variations)
                if "references" in text.lower() or "bibliography" in text.lower() or "references:" in text.lower():
                    print("References Section:", text)  # Debug print
                    is_reference_section = True
                    continue

            # Stop processing if another major section starts
            if is_reference_section:
                if any(text.lower().startswith(kw) for kw in ["figure", "tables", "acknowledgement"]):
                    print("Stopping at section:", text)
                    break
            # Add valid reference lines, cleaned of Unicode
                if text:
                    print("Valid reference:", text)  # Debug print
                    cleaned_text = unicodedata.normalize("NFKD", text)
                    references.append(cleaned_text)

        return references if references else ["No references found"]
    except Exception as e:
        logging.error(f"Error extracting references: {e}")
        return []


API_KEY = os.getenv("OPENAI_API_KEY")
OPENAI_API_URL = "https://api.openai.com/v1/chat/completions"

def extract_details(content):
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {API_KEY}",
    }
    
    payload = {
        "model": "gpt-4o-mini",
        "messages": [
            {"role": "system", "content": "You are an assistant that extracts structured data from strings."},
            {"role": "user", "content": content},
        ],
        "max_tokens": 500,
    }
    
    try:
        response = requests.post(OPENAI_API_URL, headers=headers, json=payload)
        result = response.json()

        if response.status_code == 200 and "choices" in result and result["choices"]:
            content = result["choices"][0]["message"]["content"].strip()
            content = content.replace("```json", "").replace("```", "").strip()  # Clean extraneous markdown
            return json.loads(content)  # Convert string to JSON
        else:
            return {"error": "Failed to extract details", "response": result}
    
    except Exception as e:
        return {"error": "Error interacting with OpenAI API", "details": str(e)}

@app.route("/extract", methods=["POST"])
def extract():
    data = request.json
    
    extracted_data = extract_details(data["content"])  # Convert string function to callable
    return jsonify(extracted_data)

# --- Reference Processing Utilities ---

def calculate_reference_statistics(references):
    """Calculate comprehensive statistics from processed references"""
    if not references:
        return {}

    # Fix source assignment for NOT_FOUND references
    for ref in references:
        if isinstance(ref, dict):
            ref_type = ref.get('type', '')
            ref_source = ref.get('source', '')

            # Fix NOT_FOUND references to have source='not_found' instead of 'manual'
            if ref_type == 'NOT_FOUND' and ref_source == 'manual':
                ref['source'] = 'not_found'
                # Keep needs_review=True as requested
                ref['needs_review'] = True

    stats = {
        'total_count': len(references),
        'pubmed_found': 0,
        'crossref_found': 0,
        'not_found': 0,
        'quality_scores': [],
        'high_quality': 0,  # score >= 85
        'medium_quality': 0,  # score 70-84
        'low_quality': 0,  # score < 70
        'needs_review': 0,
        'processing_sources': {},
        'confidence_levels': {'high': 0, 'medium': 0, 'low': 0}
    }

    for ref in references:
        # Count by source
        if hasattr(ref, 'type') or 'type' in ref:
            ref_type = ref.get('type', 'unknown') if isinstance(ref, dict) else getattr(ref, 'type', 'unknown')
            if 'pubmed' in ref_type.lower() or ref_type == 'FOUND':
                stats['pubmed_found'] += 1
            elif 'crossref' in ref_type.lower():
                stats['crossref_found'] += 1
            else:
                stats['not_found'] += 1

        # Quality scoring - use 0-1 scale to match our schema
        quality_score = ref.get('score', 0) if isinstance(ref, dict) else getattr(ref, 'score', 0)

        # Convert percentage scores to 0-1 scale if needed
        if quality_score > 1:
            quality_score = quality_score / 100.0

        if quality_score > 0:
            stats['quality_scores'].append(quality_score)
            if quality_score >= 0.8:  # High confidence >= 0.8
                stats['high_quality'] += 1
            elif quality_score >= 0.5:  # Medium confidence 0.5-0.79
                stats['medium_quality'] += 1
            else:  # Low confidence < 0.5
                stats['low_quality'] += 1

        # Confidence levels
        confidence = ref.get('confidence', 'medium') if isinstance(ref, dict) else getattr(ref, 'confidence', 'medium')
        if confidence in stats['confidence_levels']:
            stats['confidence_levels'][confidence] += 1

        # Needs review flag
        needs_review = ref.get('needs_review', False) if isinstance(ref, dict) else getattr(ref, 'needs_review', False)
        if needs_review:
            stats['needs_review'] += 1

    # Calculate averages
    if stats['quality_scores']:
        stats['average_quality'] = sum(stats['quality_scores']) / len(stats['quality_scores'])
        stats['min_quality'] = min(stats['quality_scores'])
        stats['max_quality'] = max(stats['quality_scores'])
    else:
        stats['average_quality'] = 0
        stats['min_quality'] = 0
        stats['max_quality'] = 0

    return stats

# --- Reference API Endpoints ---

# POST /api/references
@app.route('/api/references', methods=['POST'])
def save_processed_references():
    data = request.get_json()
    article_id = data.get('articleId')
    references = data.get('references')
    processing_metadata = data.get('processingMetadata', {})
    processed_by = data.get('processedBy', 'unknown')
    processing_source = data.get('processingSource', 'manual')

    if not article_id or references is None:
        return jsonify({'error': 'Missing articleId or references'}), 400

    try:
        # Handle article_id - ensure we have a corresponding article_files record
        article_file_record = None
        if isinstance(article_id, str):
            # Look for existing article_files record
            article_file_record = ArticleFile.query.filter_by(article_id=article_id).first()
            if not article_file_record:
                # Create new article_files record
                article_file_record = ArticleFile(
                    article_id=article_id,
                    status='processing',
                    current_stage='reference_processing',
                    created_at=datetime.utcnow()
                )
                db.session.add(article_file_record)
                db.session.flush()  # Get the ID
        else:
            # Assume it's already an integer ID
            article_file_record = ArticleFile.query.get(article_id)
            if not article_file_record:
                return jsonify({'error': f'Article file with ID {article_id} not found'}), 404

        # Use the article_files record ID for the foreign key
        article_file_id = article_file_record.id
        # Calculate comprehensive statistics from references
        stats = calculate_reference_statistics(references)

        # Enhance metadata with statistics and processing info
        enhanced_metadata = {
            **processing_metadata,
            'statistics': stats,
            'processed_at': datetime.utcnow().isoformat(),
            'total_references': len(references),
            'processing_session_id': data.get('sessionId', f"session_{int(datetime.utcnow().timestamp())}")
        }

        # Calculate analytics fields from statistics
        total_references = stats.get('total_count', len(references))
        high_confidence_count = stats.get('high_quality', 0)
        medium_confidence_count = stats.get('medium_quality', 0)
        low_confidence_count = stats.get('low_quality', 0)
        needs_review_count = stats.get('needs_review', 0)

        # Create simplified source distribution - only essential metrics
        source_distribution = {
            'pubmed_found': stats.get('pubmed_found', 0),
            'crossref_found': stats.get('crossref_found', 0),
            'not_found': stats.get('not_found', 0)
        }

        # Calculate total quality score (average) - already in 0-1 scale
        total_quality_score = stats.get('average_quality', 0)

        # Try to get existing record using the article_files ID
        record = ArticleReference.query.filter_by(article_id=article_file_id).first()
        if record:
            # Update existing record with all fields
            record.reference_text = json.dumps(references)
            record.processed_references = json.dumps(references)
            record.processing_metadata = json.dumps(enhanced_metadata)
            record.total_quality_score = total_quality_score
            record.source_distribution = json.dumps(source_distribution)
            record.total_references = total_references
            record.high_confidence_count = high_confidence_count
            record.medium_confidence_count = medium_confidence_count
            record.low_confidence_count = low_confidence_count
            record.needs_review_count = needs_review_count
            record.updated_at = datetime.utcnow()
            record.processed_by = processed_by
            record.processing_source = processing_source
        else:
            # Create new record with all fields
            record = ArticleReference(
                article_id=article_file_id,
                reference_text=json.dumps(references),
                processed_references=json.dumps(references),
                processing_metadata=json.dumps(enhanced_metadata),
                total_quality_score=total_quality_score,
                source_distribution=json.dumps(source_distribution),
                total_references=total_references,
                high_confidence_count=high_confidence_count,
                medium_confidence_count=medium_confidence_count,
                low_confidence_count=low_confidence_count,
                needs_review_count=needs_review_count,
                processed_by=processed_by,
                processing_source=processing_source
            )
            db.session.add(record)

        db.session.commit()
        return jsonify({
            'message': 'References saved with comprehensive analytics',
            'articleId': article_file_record.article_id,  # Return the original string ID
            'articleFileId': article_file_id,  # Also return the database ID
            'statistics': stats,
            'analytics': {
                'totalReferences': total_references,
                'highConfidence': high_confidence_count,
                'mediumConfidence': medium_confidence_count,
                'lowConfidence': low_confidence_count,
                'needsReview': needs_review_count,
                'sourceDistribution': source_distribution,
                'qualityScore': total_quality_score
            },
            'metadata': enhanced_metadata
        }), 200
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

# GET /api/references/<article_id>
@app.route('/api/references/<string:article_id>', methods=['GET'])
def get_references(article_id):
    # Find the article_files record first
    article_file = ArticleFile.query.filter_by(article_id=article_id).first()
    if not article_file:
        return jsonify({'error': 'Article not found'}), 404

    # Then find the article_references record using the file ID
    record = ArticleReference.query.filter_by(article_id=article_file.id).first()
    if not record:
        return jsonify({'error': 'References not found'}), 404

    # Return data in the format expected by the frontend
    response_data = record.to_dict()

    # Add the 'references' field that the frontend expects
    # Use processed_references if available, otherwise use reference_text
    if record.processed_references:
        if isinstance(record.processed_references, str):
            try:
                response_data['references'] = json.loads(record.processed_references)
            except:
                response_data['references'] = []
        else:
            response_data['references'] = record.processed_references
    elif record.reference_text:
        if isinstance(record.reference_text, str):
            try:
                response_data['references'] = json.loads(record.reference_text)
            except:
                response_data['references'] = []
        else:
            response_data['references'] = record.reference_text
    else:
        response_data['references'] = []

    return jsonify(response_data), 200

# GET /api/article-ids - Get all article IDs for autocomplete
@app.route('/api/article-ids', methods=['GET'])
def get_article_ids():
    """Get all article IDs for autocomplete functionality"""
    try:
        # Get search query parameter for filtering
        search = request.args.get('search', '').strip()
        limit = min(int(request.args.get('limit', 10)), 50)  # Max 50 results

        # Join with ArticleFile to get the actual article identifiers
        query = db.session.query(ArticleFile.article_id).join(
            ArticleReferences, ArticleFile.id == ArticleReferences.article_id
        )

        if search:
            # Filter by article IDs that contain the search term (case-insensitive)
            query = query.filter(ArticleFile.article_id.ilike(f'%{search}%'))

        # Order by article_id and limit results
        results = query.order_by(ArticleFile.article_id).limit(limit).all()

        article_ids = [result[0] for result in results]

        return jsonify({
            'articleIds': article_ids,
            'total': len(article_ids),
            'hasMore': len(article_ids) == limit
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# PUT /api/references/<string:article_id>
@app.route('/api/references/<string:article_id>', methods=['PUT'])
def update_references(article_id):
    data = request.get_json()
    references = data.get('references')
    if references is None:
        return jsonify({'error': 'Missing references'}), 400
    record = ArticleReferences.query.filter_by(article_id=article_id).first()
    if not record:
        return jsonify({'error': 'Not found'}), 404
    try:
        record.reference_text = json.dumps(references)
        record.updated_at = datetime.utcnow()
        db.session.commit()
        return jsonify({'message': 'References updated', 'articleId': article_id}), 200
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

# ===== HEALTH CHECK ENDPOINT =====
@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint for monitoring"""
    try:
        # Test database connection
        db.session.execute(text('SELECT 1'))

        # Get basic stats
        journal_count = JournalAbbreviation.query.count()
        article_count = ArticleReferences.query.count()
        admin_count = AdminUser.query.count()

        return jsonify({
            'status': 'healthy',
            'database': 'connected',
            'timestamp': datetime.utcnow().isoformat(),
            'stats': {
                'journals': journal_count,
                'articles': article_count,
                'admins': admin_count
            },
            'version': '1.0.0'
        }), 200

    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'database': 'disconnected',
            'error': str(e),
            'timestamp': datetime.utcnow().isoformat()
        }), 500

# ===== JOURNAL ABBREVIATION CRUD ENDPOINTS =====

@app.route('/api/journals', methods=['GET'])
def get_journals():
    """
    Get all journal abbreviations with optional filtering and pagination
    """
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        search = request.args.get('search', '', type=str)
        source = request.args.get('source', '', type=str)
        verified_only = request.args.get('verified_only', False, type=bool)

        # Build query
        query = JournalAbbreviation.query

        # Apply filters
        if search:
            query = query.filter(
                or_(
                    JournalAbbreviation.full_name.ilike(f'%{search}%'),
                    JournalAbbreviation.abbreviation.ilike(f'%{search}%')
                )
            )

        if source:
            query = query.filter(JournalAbbreviation.source == source)

        if verified_only:
            query = query.filter(JournalAbbreviation.is_verified == True)

        # Order by usage count (most used first), then by name
        query = query.order_by(JournalAbbreviation.usage_count.desc(), JournalAbbreviation.full_name)

        # Paginate
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)
        journals = pagination.items

        return jsonify({
            'success': True,
            'data': [journal.to_dict() for journal in journals],
            'pagination': {
                'page': page,
                'pages': pagination.pages,
                'per_page': per_page,
                'total': pagination.total,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/journals', methods=['POST'])
def create_journal():
    """
    Create a new journal abbreviation
    """
    try:
        data = request.get_json()

        if not data or not data.get('fullName') or not data.get('abbreviation'):
            return jsonify({
                'success': False,
                'error': 'fullName and abbreviation are required'
            }), 400

        # Check if journal already exists
        existing = JournalAbbreviation.query.filter_by(full_name=data['fullName']).first()
        if existing:
            return jsonify({
                'success': False,
                'error': 'Journal with this name already exists'
            }), 409

        journal = JournalAbbreviation(
            full_name=data['fullName'],
            abbreviation=data['abbreviation'],
            source=data.get('source', 'manual'),
            confidence_score=data.get('confidenceScore', 1.0),
            is_verified=data.get('isVerified', False),
            created_by=data.get('createdBy', 'admin')
        )

        db.session.add(journal)
        db.session.commit()

        return jsonify({
            'success': True,
            'data': journal.to_dict(),
            'message': 'Journal abbreviation created successfully'
        }), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/journals/<int:journal_id>', methods=['PUT'])
def update_journal(journal_id):
    """
    Update an existing journal abbreviation
    """
    try:
        journal = JournalAbbreviation.query.get_or_404(journal_id)
        data = request.get_json()

        if not data:
            return jsonify({'success': False, 'error': 'No data provided'}), 400

        # Update fields
        if 'fullName' in data:
            # Check if new name conflicts with existing journal
            existing = JournalAbbreviation.query.filter(
                JournalAbbreviation.full_name == data['fullName'],
                JournalAbbreviation.id != journal_id
            ).first()
            if existing:
                return jsonify({
                    'success': False,
                    'error': 'Journal with this name already exists'
                }), 409
            journal.full_name = data['fullName']

        if 'abbreviation' in data:
            journal.abbreviation = data['abbreviation']
        if 'source' in data:
            journal.source = data['source']
        if 'confidenceScore' in data:
            journal.confidence_score = data['confidenceScore']
        if 'isVerified' in data:
            journal.is_verified = data['isVerified']

        journal.updated_at = datetime.utcnow()
        db.session.commit()

        return jsonify({
            'success': True,
            'data': journal.to_dict(),
            'message': 'Journal abbreviation updated successfully'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/journals/<int:journal_id>', methods=['DELETE'])
def delete_journal(journal_id):
    """
    Delete a journal abbreviation
    """
    try:
        journal = JournalAbbreviation.query.get_or_404(journal_id)

        db.session.delete(journal)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Journal abbreviation deleted successfully'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/journals/search', methods=['POST'])
def search_journal_abbreviation():
    """
    Search for journal abbreviation in database first, then fallback to GenAI
    """
    try:
        data = request.get_json()
        search_term = data.get('searchTerm', '').strip()

        if not search_term:
            return jsonify({'success': False, 'error': 'searchTerm is required'}), 400

        start_time = time.time()

        # Use the proven working search algorithm from the test
        import re

        journal = None

        # 1. Check if search term is already an abbreviation
        journal = JournalAbbreviation.query.filter(
            func.lower(JournalAbbreviation.abbreviation) == func.lower(search_term)
        ).first()

        # 2. Exact match on full name (case-insensitive)
        if not journal:
            journal = JournalAbbreviation.query.filter(
                func.lower(JournalAbbreviation.full_name) == func.lower(search_term)
            ).first()

        # 3. Try variations if exact match fails
        if not journal:
            variations = []

            # Replace & with 'and'
            if '&' in search_term:
                variations.append(search_term.replace('&', 'and'))
                variations.append(search_term.replace('&', ' and '))

            # Normalize spaces
            normalized_spaces = re.sub(r'\s+', ' ', search_term.strip())
            if normalized_spaces != search_term:
                variations.append(normalized_spaces)

            # Try each variation
            for variation in variations:
                journal = JournalAbbreviation.query.filter(
                    func.lower(JournalAbbreviation.full_name) == func.lower(variation)
                ).first()
                if journal:
                    break

        # 4. Fuzzy matching with normalization (if still not found)
        if not journal:
            def normalize_for_matching(text):
                if not text:
                    return ""
                text = text.lower()
                text = text.replace('&', 'and')
                text = re.sub(r'\s+', ' ', text)
                text = re.sub(r'[,.:;()\-]', '', text)
                text = re.sub(r'^the\s+', '', text)
                return text.strip()

            normalized_search = normalize_for_matching(search_term)
            all_journals = JournalAbbreviation.query.all()

            for j in all_journals:
                normalized_db = normalize_for_matching(j.full_name)
                if normalized_db == normalized_search:
                    journal = j
                    break

        # 5. Partial matching (contains) as last resort
        if not journal:
            journal = JournalAbbreviation.query.filter(
                func.lower(JournalAbbreviation.full_name).contains(func.lower(search_term))
            ).first()

        if journal:
            # Update usage count
            journal.usage_count += 1
            db.session.commit()

            search_time = int((time.time() - start_time) * 1000)

            return jsonify({
                'success': True,
                'data': {
                    'abbreviation': journal.abbreviation,
                    'source': 'database',
                    'confidence': journal.confidence_score,
                    'searchTimeMs': search_time
                }
            })

        # If not found in database, return indication to use GenAI
        search_time = int((time.time() - start_time) * 1000)

        return jsonify({
            'success': True,
            'data': {
                'abbreviation': None,
                'source': 'not_found',
                'searchTimeMs': search_time,
                'message': 'Not found in database, use GenAI fallback'
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/journals/test-search', methods=['POST'])
def test_journal_search():
    """
    Test endpoint to verify fuzzy matching works
    """
    try:
        data = request.get_json()
        search_terms = data.get('searchTerms', [])

        def normalize_journal_name(name):
            """Normalize journal name for fuzzy matching"""
            import re
            name = name.lower()
            name = re.sub(r'^(the\s+)', '', name)
            name = re.sub(r'\s+', ' ', name)
            name = name.replace('&', 'and')
            name = name.replace(',', '')
            name = name.replace('.', '')
            name = name.replace(':', '')
            name = name.replace(';', '')
            name = name.strip()
            return name

        results = []
        for term in search_terms:
            normalized = normalize_journal_name(term)

            # Find matches
            matches = []
            all_journals = JournalAbbreviation.query.all()
            for j in all_journals:
                if normalize_journal_name(j.full_name) == normalized:
                    matches.append({
                        'id': j.id,
                        'fullName': j.full_name,
                        'abbreviation': j.abbreviation,
                        'normalized': normalize_journal_name(j.full_name)
                    })

            results.append({
                'searchTerm': term,
                'normalized': normalized,
                'matches': matches
            })

        return jsonify({
            'success': True,
            'data': results
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

if __name__ == "__main__":
    app.run(debug=True, port=4999)
