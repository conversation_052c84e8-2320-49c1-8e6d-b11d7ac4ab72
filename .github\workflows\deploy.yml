name: Deploy EDITINK to AWS EC2

on:
  push:
    branches: [ main ]
    paths: [ 'TE_BACK/**' ]  # Only deploy when backend changes
  workflow_dispatch:         # Allow manual deployment

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: production

    steps:
    - name: 🚀 Deploy to EC2
      uses: appleboy/ssh-action@v0.1.7
      with:
        host: ${{ secrets.EC2_HOST }}
        username: ${{ secrets.EC2_USER }}
        key: ${{ secrets.EC2_SSH_KEY }}
        script: |
          echo "🔄 Starting deployment..."

          # Navigate to app directory
          cd /var/www/editink

          # Create backup
          sudo cp -r TE_BACK TE_BACK.backup.$(date +%Y%m%d_%H%M%S) || true

          # Pull latest changes
          echo "📥 Pulling latest changes..."
          git pull origin main

          # Setup backend
          cd TE_BACK

          # Activate virtual environment (create if doesn't exist)
          if [ ! -d "venv" ]; then
            echo "🐍 Creating virtual environment..."
            python3 -m venv venv
          fi
          source venv/bin/activate

          # Install dependencies
          echo "📦 Installing dependencies..."
          pip install --upgrade pip
          pip install -r requirements.txt

          # Setup environment file if needed
          if [ ! -f ".env" ]; then
            echo "⚙️  Creating basic environment file..."
            cat > .env << EOF
          # Flask Configuration
          FLASK_ENV=production
          SECRET_KEY=$(python3 -c "import secrets; print(secrets.token_hex(32))")

          # OpenAI API Configuration (optional)
          OPENAI_API_KEY=${OPENAI_API_KEY:-}
          EOF
          fi

          # Initialize SQLite database if it doesn't exist
          if [ ! -f "article_references.db" ]; then
            echo "🗄️  Initializing SQLite database..."
            python setup_sqlite.py
          fi

          # Setup systemd service if it doesn't exist
          if ! sudo systemctl is-enabled editink-backend >/dev/null 2>&1; then
            echo "🔧 Setting up systemd service..."
            sudo tee /etc/systemd/system/editink-backend.service > /dev/null <<EOF
          [Unit]
          Description=EDITINK Backend API
          After=network.target

          [Service]
          Type=exec
          User=$USER
          Group=$USER
          WorkingDirectory=/var/www/editink/TE_BACK
          Environment=PATH=/var/www/editink/TE_BACK/venv/bin
          ExecStart=/var/www/editink/TE_BACK/venv/bin/gunicorn --bind 0.0.0.0:4999 --workers 2 --timeout 120 app:app
          ExecReload=/bin/kill -s HUP \$MAINPID
          Restart=always
          RestartSec=3

          [Install]
          WantedBy=multi-user.target
          EOF
            sudo systemctl daemon-reload
            sudo systemctl enable editink-backend
          fi

          # Restart service
          echo "🔄 Restarting service..."
          sudo systemctl restart editink-backend

          # Wait for service to start
          echo "⏳ Waiting for service to start..."
          sleep 10

          # Health check with retries
          echo "🏥 Running health check..."
          for i in {1..5}; do
            if curl -f http://localhost:4999/health >/dev/null 2>&1; then
              echo "✅ Deployment successful! Health check passed."
              exit 0
            fi
            echo "⏳ Health check attempt $i/5 failed, retrying..."
            sleep 5
          done

          echo "❌ Deployment failed - health check failed after 5 attempts"
          echo "📋 Service status:"
          sudo systemctl status editink-backend --no-pager
          echo "📋 Recent logs:"
          sudo journalctl -u editink-backend -n 20 --no-pager
          exit 1
