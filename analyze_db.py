#!/usr/bin/env python3
"""
Database analysis script for EDITINK
Analyzes the current database structure and content
"""
import os
import sys
import json
from datetime import datetime

# Add the current directory to Python path to import app
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db, User, ArticleFile, ArticleReference, JournalAbbreviation

def analyze_database():
    """
    Comprehensive database analysis
    """
    print("🔍 EDITINK Database Analysis")
    print("=" * 60)
    
    try:
        with app.app_context():
            # ===== TABLE STRUCTURE =====
            print("\n📊 DATABASE STRUCTURE")
            print("-" * 30)
            
            inspector = db.inspect(db.engine)
            tables = inspector.get_table_names()
            
            print(f"Total tables: {len(tables)}")
            for table in tables:
                columns = inspector.get_columns(table)
                print(f"\n🗂️  {table.upper()} ({len(columns)} columns)")
                for col in columns:
                    nullable = "NULL" if col.get('nullable', True) else "NOT NULL"
                    primary_key = " [PK]" if col.get('primary_key', False) else ""
                    print(f"   • {col['name']:<25} {str(col['type']):<15} {nullable}{primary_key}")
            
            # ===== DATA COUNTS =====
            print(f"\n📈 DATA SUMMARY")
            print("-" * 30)
            
            user_count = User.query.count()
            article_file_count = ArticleFile.query.count()
            article_ref_count = ArticleReference.query.count()
            journal_count = JournalAbbreviation.query.count()
            
            print(f"👥 Users:                {user_count:>8}")
            print(f"📄 Article Files:        {article_file_count:>8}")
            print(f"📚 Article References:   {article_ref_count:>8}")
            print(f"📖 Journal Abbreviations:{journal_count:>8}")
            
            # ===== USER ANALYSIS =====
            if user_count > 0:
                print(f"\n👥 USER BREAKDOWN")
                print("-" * 30)
                
                # Role distribution
                roles = db.session.execute(
                    "SELECT role, COUNT(*) as count FROM users GROUP BY role"
                ).fetchall()
                
                for role, count in roles:
                    print(f"   {role:<12}: {count:>3}")
                
                # Active vs inactive
                active_users = User.query.filter_by(is_active=True).count()
                inactive_users = user_count - active_users
                print(f"   Active      : {active_users:>3}")
                print(f"   Inactive    : {inactive_users:>3}")
                
                # Recent users
                recent_users = User.query.order_by(User.created_at.desc()).limit(3).all()
                print(f"\n   Recent users:")
                for user in recent_users:
                    created = user.created_at.strftime('%Y-%m-%d') if user.created_at else 'Unknown'
                    print(f"   • {user.username} ({user.role}) - {created}")
            
            # ===== ARTICLE ANALYSIS =====
            if article_file_count > 0:
                print(f"\n📄 ARTICLE FILES BREAKDOWN")
                print("-" * 30)
                
                # Status distribution
                statuses = db.session.execute(
                    "SELECT status, COUNT(*) as count FROM article_files GROUP BY status"
                ).fetchall()
                
                for status, count in statuses:
                    print(f"   {status:<15}: {count:>3}")
                
                # Priority distribution
                priorities = db.session.execute(
                    "SELECT priority, COUNT(*) as count FROM article_files GROUP BY priority"
                ).fetchall()
                
                print(f"\n   Priority breakdown:")
                for priority, count in priorities:
                    print(f"   {priority:<8}: {count:>3}")
                
                # Recent articles
                recent_articles = ArticleFile.query.order_by(ArticleFile.created_at.desc()).limit(5).all()
                print(f"\n   Recent articles:")
                for article in recent_articles:
                    created = article.created_at.strftime('%Y-%m-%d') if article.created_at else 'Unknown'
                    print(f"   • {article.article_id} ({article.status}) - {created}")
            
            # ===== REFERENCE ANALYSIS =====
            if article_ref_count > 0:
                print(f"\n📚 REFERENCE ANALYSIS")
                print("-" * 30)
                
                # Total references processed
                total_refs = db.session.execute(
                    "SELECT SUM(total_references) FROM article_references WHERE total_references IS NOT NULL"
                ).scalar() or 0
                
                print(f"   Total references processed: {total_refs:>6}")
                
                # Quality distribution
                quality_stats = db.session.execute("""
                    SELECT 
                        SUM(high_confidence_count) as high,
                        SUM(medium_confidence_count) as medium,
                        SUM(low_confidence_count) as low,
                        SUM(needs_review_count) as needs_review
                    FROM article_references
                """).fetchone()
                
                if quality_stats:
                    print(f"   High confidence:        {quality_stats[0] or 0:>6}")
                    print(f"   Medium confidence:      {quality_stats[1] or 0:>6}")
                    print(f"   Low confidence:         {quality_stats[2] or 0:>6}")
                    print(f"   Needs review:           {quality_stats[3] or 0:>6}")
                
                # Average references per article
                if article_ref_count > 0:
                    avg_refs = total_refs / article_ref_count
                    print(f"   Avg refs per article:   {avg_refs:>6.1f}")
                
                # Processing sources
                sources = db.session.execute(
                    "SELECT processing_source, COUNT(*) as count FROM article_references WHERE processing_source IS NOT NULL GROUP BY processing_source"
                ).fetchall()
                
                if sources:
                    print(f"\n   Processing sources:")
                    for source, count in sources:
                        print(f"   {source:<15}: {count:>3}")
            
            # ===== JOURNAL ANALYSIS =====
            if journal_count > 0:
                print(f"\n📖 JOURNAL ANALYSIS")
                print("-" * 30)
                
                # Source distribution
                sources = db.session.execute(
                    "SELECT source, COUNT(*) as count FROM journal_abbreviations GROUP BY source"
                ).fetchall()
                
                print(f"   Source breakdown:")
                for source, count in sources:
                    print(f"   {source:<12}: {count:>3}")
                
                # Verification status
                verified = JournalAbbreviation.query.filter_by(is_verified=True).count()
                unverified = journal_count - verified
                print(f"\n   Verified:     {verified:>3}")
                print(f"   Unverified:   {unverified:>3}")
                
                # Top used journals
                top_journals = JournalAbbreviation.query.order_by(
                    JournalAbbreviation.usage_count.desc()
                ).limit(5).all()
                
                print(f"\n   Most used journals:")
                for journal in top_journals:
                    usage = journal.usage_count or 0
                    print(f"   • {journal.full_name[:40]:<40} ({usage} uses)")
            
            print(f"\n✅ Database analysis completed successfully!")
            return True
            
    except Exception as e:
        print(f"❌ Error analyzing database: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    analyze_database()
