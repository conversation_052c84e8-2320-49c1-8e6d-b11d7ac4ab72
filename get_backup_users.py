#!/usr/bin/env python3
"""
Get users from backup database
"""
import sqlite3

def get_backup_users():
    """
    Extract user information from backup database
    """
    print("👥 EXTRACTING USERS FROM BACKUP DATABASE")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('server_backup_20250915_134533.db')
        cursor = conn.cursor()
        
        # Find user tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%user%'")
        user_tables = cursor.fetchall()
        print(f"User tables found: {user_tables}")
        
        if user_tables:
            table_name = user_tables[0][0]  # Get first user table
            print(f"\nChecking table: {table_name}")
            
            # Get table structure
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns_info = cursor.fetchall()
            columns = [col[1] for col in columns_info]
            print(f"Columns: {columns}")
            
            # Get all users
            cursor.execute(f"SELECT * FROM {table_name}")
            users = cursor.fetchall()
            
            print(f"\n📊 Found {len(users)} user(s) in backup:")
            
            for i, user in enumerate(users, 1):
                print(f"\n{i}. User Details:")
                for j, value in enumerate(user):
                    if j < len(columns):
                        column_name = columns[j]
                        if 'password' in column_name.lower():
                            print(f"   {column_name}: {value}")
                        else:
                            print(f"   {column_name}: {value}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    get_backup_users()
