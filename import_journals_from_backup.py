#!/usr/bin/env python3
"""
Import journal abbreviations from server backup to current database
Extracts 2,414 journals from backup while keeping the current clean schema
"""
import os
import sys
import sqlite3
from datetime import datetime

# Add the current directory to Python path to import app
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def find_backup_file():
    """
    Find the most recent server backup file
    """
    import glob
    backup_files = glob.glob('server_backup_*.db')
    
    if not backup_files:
        print("❌ No server backup files found!")
        return None
    
    # Get the most recent backup
    backup_files.sort(reverse=True)  # Most recent first
    backup_file = backup_files[0]
    
    print(f"📦 Using backup file: {backup_file}")
    size = os.path.getsize(backup_file)
    print(f"   Size: {size/1024/1024:.1f} MB")
    
    return backup_file

def extract_journals_from_backup(backup_file):
    """
    Extract journal data from the server backup database
    """
    print(f"\n🔍 Extracting journals from backup...")
    
    try:
        conn = sqlite3.connect(backup_file)
        cursor = conn.cursor()
        
        # Check what tables exist in backup
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [row[0] for row in cursor.fetchall()]
        print(f"   Tables in backup: {tables}")
        
        # Find the journal table (could be journal_abbreviations or similar)
        journal_table = None
        for table in tables:
            if 'journal' in table.lower():
                journal_table = table
                break
        
        if not journal_table:
            print("❌ No journal table found in backup!")
            return None
        
        print(f"   Found journal table: {journal_table}")
        
        # Get table structure
        cursor.execute(f"PRAGMA table_info({journal_table});")
        columns_info = cursor.fetchall()
        columns = [col[1] for col in columns_info]  # Column names
        print(f"   Columns: {columns}")
        
        # Extract all journal data
        cursor.execute(f"SELECT * FROM {journal_table};")
        journal_data = cursor.fetchall()
        
        print(f"✅ Extracted {len(journal_data)} journals from backup")
        
        conn.close()
        
        return {
            'table_name': journal_table,
            'columns': columns,
            'data': journal_data
        }
        
    except Exception as e:
        print(f"❌ Error extracting journals: {e}")
        return None

def import_journals_to_current_db(journal_info):
    """
    Import journals into the current database using Flask models
    """
    print(f"\n📥 Importing journals to current database...")
    
    try:
        from app import app, db, JournalAbbreviation
        
        with app.app_context():
            # Clear existing sample journals first
            print("🗑️  Clearing existing sample journals...")
            existing_count = JournalAbbreviation.query.count()
            print(f"   Found {existing_count} existing journals")
            
            if existing_count > 0:
                JournalAbbreviation.query.delete()
                db.session.commit()
                print("   Cleared existing journals")
            
            # Map backup columns to current schema
            columns = journal_info['columns']
            data = journal_info['data']
            
            print(f"📋 Mapping columns from backup to current schema...")
            print(f"   Backup columns: {columns}")
            
            # Create column mapping
            column_mapping = {}
            for i, col in enumerate(columns):
                if col.lower() in ['id']:
                    column_mapping['id'] = i
                elif col.lower() in ['full_name', 'fullname']:
                    column_mapping['full_name'] = i
                elif col.lower() in ['abbreviation', 'abbrev']:
                    column_mapping['abbreviation'] = i
                elif col.lower() in ['source']:
                    column_mapping['source'] = i
                elif col.lower() in ['confidence_score', 'confidence']:
                    column_mapping['confidence_score'] = i
                elif col.lower() in ['usage_count', 'usage']:
                    column_mapping['usage_count'] = i
                elif col.lower() in ['is_verified', 'verified']:
                    column_mapping['is_verified'] = i
                elif col.lower() in ['created_at', 'created']:
                    column_mapping['created_at'] = i
                elif col.lower() in ['updated_at', 'updated']:
                    column_mapping['updated_at'] = i
                elif col.lower() in ['created_by', 'creator']:
                    column_mapping['created_by'] = i
            
            print(f"   Column mapping: {column_mapping}")
            
            # Import journals in batches
            batch_size = 100
            imported_count = 0
            
            for i in range(0, len(data), batch_size):
                batch = data[i:i + batch_size]
                
                for row in batch:
                    try:
                        # Create journal object with mapped data
                        journal_data = {}
                        
                        # Required fields
                        if 'full_name' in column_mapping:
                            journal_data['full_name'] = row[column_mapping['full_name']] or 'Unknown Journal'
                        else:
                            journal_data['full_name'] = 'Unknown Journal'
                            
                        if 'abbreviation' in column_mapping:
                            journal_data['abbreviation'] = row[column_mapping['abbreviation']] or journal_data['full_name']
                        else:
                            journal_data['abbreviation'] = journal_data['full_name']
                        
                        # Optional fields with defaults
                        journal_data['source'] = row[column_mapping['source']] if 'source' in column_mapping and row[column_mapping['source']] else 'imported'
                        journal_data['confidence_score'] = row[column_mapping['confidence_score']] if 'confidence_score' in column_mapping and row[column_mapping['confidence_score']] else 1.0
                        journal_data['usage_count'] = row[column_mapping['usage_count']] if 'usage_count' in column_mapping and row[column_mapping['usage_count']] else 0
                        journal_data['is_verified'] = bool(row[column_mapping['is_verified']]) if 'is_verified' in column_mapping and row[column_mapping['is_verified']] is not None else True
                        journal_data['created_by'] = row[column_mapping['created_by']] if 'created_by' in column_mapping and row[column_mapping['created_by']] else 'import_script'
                        
                        # Create journal
                        journal = JournalAbbreviation(**journal_data)
                        db.session.add(journal)
                        imported_count += 1
                        
                    except Exception as e:
                        print(f"⚠️  Error importing journal row {imported_count + 1}: {e}")
                        continue
                
                # Commit batch
                try:
                    db.session.commit()
                    print(f"   Imported batch: {imported_count} journals so far...")
                except Exception as e:
                    print(f"⚠️  Error committing batch: {e}")
                    db.session.rollback()
            
            print(f"✅ Successfully imported {imported_count} journals!")
            
            # Verify import
            final_count = JournalAbbreviation.query.count()
            print(f"🔍 Verification: {final_count} journals now in database")
            
            return imported_count
            
    except Exception as e:
        print(f"❌ Error importing journals: {e}")
        import traceback
        traceback.print_exc()
        return 0

def verify_import():
    """
    Verify the import was successful
    """
    print(f"\n🔍 Verifying journal import...")
    
    try:
        from app import app, db, JournalAbbreviation
        
        with app.app_context():
            total_journals = JournalAbbreviation.query.count()
            print(f"📊 Total journals in database: {total_journals}")
            
            # Source distribution
            sources = db.session.execute(
                "SELECT source, COUNT(*) FROM journal_abbreviations GROUP BY source ORDER BY COUNT(*) DESC"
            ).fetchall()
            
            print(f"📋 Source distribution:")
            for source, count in sources:
                print(f"   - {source}: {count}")
            
            # Sample journals
            sample_journals = JournalAbbreviation.query.limit(5).all()
            print(f"📖 Sample journals:")
            for journal in sample_journals:
                print(f"   • {journal.full_name} → {journal.abbreviation}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error verifying import: {e}")
        return False

def main():
    """
    Main import process
    """
    print("📥 JOURNAL IMPORT FROM BACKUP")
    print("=" * 60)
    print("This will:")
    print("1. Find the server backup file")
    print("2. Extract 2,414 journal abbreviations")
    print("3. Import them into current database")
    print("4. Keep everything else unchanged")
    print()
    
    # Step 1: Find backup file
    backup_file = find_backup_file()
    if not backup_file:
        return False
    
    # Step 2: Extract journals from backup
    journal_info = extract_journals_from_backup(backup_file)
    if not journal_info:
        return False
    
    # Step 3: Import journals to current database
    imported_count = import_journals_to_current_db(journal_info)
    if imported_count == 0:
        print("💥 Journal import failed!")
        return False
    
    # Step 4: Verify import
    if not verify_import():
        print("⚠️  Import verification failed")
    
    print(f"\n" + "=" * 60)
    print("🎉 JOURNAL IMPORT COMPLETED!")
    print()
    print("📋 Summary:")
    print(f"   - Imported: {imported_count} journal abbreviations")
    print(f"   - Source: {backup_file}")
    print(f"   - Current database: Clean schema + imported journals")
    print(f"   - Articles: Still 0 (unchanged)")
    print(f"   - Users: Still 0 (unchanged)")
    print()
    print("✅ Your database now has the comprehensive journal collection!")
    
    return True

if __name__ == "__main__":
    main()
